import * as request from 'supertest';

/**
 * Model ID Filtering E2E Tests
 *
 * This test suite validates Model ID filtering functionality in the
 * GET /feed/user/{userIdentifier} and GET /feed/discovery endpoints with proper test assertions.
 *
 * **Environment Setup Required**:
 * export E2E_TEST_BASE_HOST=https://private-api.letzai.gonser.com.br
 * export E2E_TEST_USERNAME=mischstrotz
 * export E2E_TEST_PASSWORD=L3tzAI@2023!
 */
describe('Model ID Filtering E2E Tests', () => {
  // Environment configuration
  const baseHost = process.env.E2E_TEST_BASE_HOST;
  const testUsername = process.env.E2E_TEST_USERNAME;
  const testPassword = process.env.E2E_TEST_PASSWORD;

  let userToken: string;
  let testUserIdentifier: string;
  let availableModelIds: string[] = [];
  let baselineResponse: any;

  beforeAll(async () => {
    // Validate required environment variables
    if (!baseHost) {
      throw new Error('E2E_TEST_BASE_HOST environment variable is required');
    }
    if (!testUsername) {
      throw new Error('E2E_TEST_USERNAME environment variable is required');
    }
    if (!testPassword) {
      throw new Error('E2E_TEST_PASSWORD environment variable is required');
    }

    console.log('\n🧪 Model ID Filtering E2E Tests');
    console.log(`📍 Target API: ${baseHost}`);
    console.log(`👤 Test User: ${testUsername}`);

    try {
      // Perform actual authentication against the API
      const authResponse = await request(baseHost)
        .post('/auth/login')
        .send({
          username: testUsername,
          password: testPassword
        })
        .expect(200);

      // Extract JWT token from response
      if (!authResponse.body.jwtToken) {
        throw new Error('Authentication response missing jwtToken field');
      }

      userToken = authResponse.body.jwtToken;
      testUserIdentifier = testUsername;
      console.log('✅ Authentication successful');
    } catch (error) {
      console.error('❌ Authentication failed:', error.message);
      throw error;
    }
  });

  describe('Model ID Filtering Functionality', () => {
    it('should return all user content when no modelIds parameter is provided (baseline)', async () => {
      console.log('\n📋 Test 1: Baseline (no filtering)');

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ limit: 50, entityType: 'image' })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);

      // Store baseline for comparison
      baselineResponse = response.body;

      // Extract model IDs from the response for use in subsequent tests
      if (response.body.items && response.body.items.length > 0) {
        console.log('🔍 Debugging first few items structure:');

        // Check first 10 items to see if any have model information
        const itemsToCheck = response.body.items.slice(0, 10);

        // Count types
        const typeCounts = response.body.items.reduce((counts: any, item: any) => {
          counts[item.type] = (counts[item.type] || 0) + 1;
          return counts;
        }, {});
        console.log(`   Content type distribution: ${JSON.stringify(typeCounts)}`);
        itemsToCheck.forEach((item: any, index: number) => {
          console.log(`   Item ${index + 1}:`);
          console.log(`     - Type: ${item.type}`);
          console.log(`     - Has imageCompletion: ${!!item.imageCompletion}`);
          console.log(`     - Has video: ${!!item.video}`);

          if (item.imageCompletion) {
            console.log(`     - imageCompletion.models length: ${item.imageCompletion.models?.length || 0}`);
            console.log(`     - imageCompletion.models: ${JSON.stringify(item.imageCompletion.models?.slice(0, 1) || [])}`);
            console.log(`     - imageCompletion.modelId: ${item.imageCompletion.modelId}`);
            console.log(`     - imageCompletion.metadata: ${JSON.stringify(item.imageCompletion.metadata)}`);
            console.log(`     - imageCompletion keys: ${Object.keys(item.imageCompletion).join(', ')}`);
          }

          if (item.video) {
            console.log(`     - video.modelId: ${item.video.modelId}`);
            console.log(`     - video.originalImageCompletionId: ${item.video.originalImageCompletionId}`);
          }
        });

        response.body.items.forEach((item: any, index: number) => {
          let modelId = null;

          // Check for direct modelId field
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            modelId = item.imageCompletion.modelId;
            console.log(`   Item ${index + 1}: Found modelId from direct field: ${modelId}`);
          } else if (item.type === 'video' && item.video?.modelId) {
            modelId = item.video.modelId;
            console.log(`   Item ${index + 1}: Found modelId from video: ${modelId}`);
          }

          // Check for models array with first model's ID
          if (!modelId && item.type === 'image' && item.imageCompletion?.models?.length > 0) {
            modelId = item.imageCompletion.models[0].id;
            console.log(`   Item ${index + 1}: Found modelId from models array: ${modelId}`);
          }

          if (modelId && !availableModelIds.includes(modelId)) {
            availableModelIds.push(modelId);
            console.log(`   Item ${index + 1}: Added new modelId: ${modelId}`);
          }
        });

        console.log(`🎯 Available model IDs found: ${availableModelIds.length}`);
        console.log(`📝 Model IDs: ${availableModelIds.slice(0, 3).join(', ')}${availableModelIds.length > 3 ? '...' : ''}`);
      } else {
        console.log('⚠️  No items found in baseline response');
      }

      expect(response.body.items).toBeDefined();
      console.log('✅ Baseline test completed successfully');
    });

    it('should behave same as no parameter when modelIds[] is empty', async () => {
      console.log('\n📋 Test 2: Empty filter array');

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ 'modelIds[]': '', limit: 50 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);
      console.log(`📊 Baseline items: ${baselineResponse?.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // Should return same amount as baseline (or similar, accounting for potential new content)
      const itemCountDiff = Math.abs((response.body.items?.length || 0) - (baselineResponse?.items?.length || 0));
      console.log(`📈 Item count difference from baseline: ${itemCountDiff}`);

      // Allow small differences due to potential new content, but should be roughly the same
      expect(itemCountDiff).toBeLessThanOrEqual(5);
      console.log('✅ Empty filter array test completed');
    });

    it('should return only content created with the specified model when filtering by single model ID', async () => {
      console.log('\n📋 Test 3: Single valid model ID filtering');

      // This test will fail if model ID filtering is broken
      if (availableModelIds.length === 0) {
        throw new Error('No model IDs available from baseline - cannot test model filtering. This indicates that the user content does not have model ID information.');
      }

      const testModelId = availableModelIds[0];
      console.log(`🎯 Testing with model ID: ${testModelId}`);

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ 'modelIds[]': testModelId, limit: 50 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // CRITICAL TEST: Model ID filtering should return content with the specified model ID
      // If this fails, it indicates the model ID filtering functionality is broken
      expect(response.body.items.length).toBeGreaterThan(0);

      // Verify all returned items have the correct model ID
      response.body.items.forEach((item: any, index: number) => {
        let itemModelId = null;
        if (item.type === 'image' && item.imageCompletion?.modelId) {
          itemModelId = item.imageCompletion.modelId;
        } else if (item.type === 'video' && item.video?.modelId) {
          itemModelId = item.video.modelId;
        }

        console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
        expect(itemModelId).toBe(testModelId);
      });

      console.log('✅ Single model ID filtering test passed');
    });

    it('should return content created with any of the specified models when filtering by multiple model IDs', async () => {
      console.log('\n📋 Test 4: Multiple valid model IDs filtering');

      // This test will fail if model ID filtering is broken
      if (availableModelIds.length < 2) {
        throw new Error('Need at least 2 model IDs for multiple model ID filtering test. This indicates insufficient model ID diversity in user content.');
      }

      const testModelIds = availableModelIds.slice(0, 2);
      console.log(`🎯 Testing with model IDs: ${testModelIds.join(', ')}`);

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ 'modelIds[]': testModelIds, limit: 50 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // CRITICAL TEST: Multiple model ID filtering should return content with any of the specified model IDs
      // If this fails, it indicates the model ID filtering functionality is broken
      expect(response.body.items.length).toBeGreaterThan(0);

      // Verify all returned items have one of the specified model IDs
      response.body.items.forEach((item: any, index: number) => {
        let itemModelId = null;
        if (item.type === 'image' && item.imageCompletion?.modelId) {
          itemModelId = item.imageCompletion.modelId;
        } else if (item.type === 'video' && item.video?.modelId) {
          itemModelId = item.video.modelId;
        }

        console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
        expect(testModelIds).toContain(itemModelId);
      });

      console.log('✅ Multiple model IDs filtering test passed');
    });

    it('should return empty array when filtering by invalid/non-existent model IDs', async () => {
      console.log('\n📋 Test 5: Invalid/non-existent model IDs');

      const invalidModelId = 'invalid-model-id-12345';
      console.log(`🎯 Testing with invalid model ID: ${invalidModelId}`);

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ 'modelIds[]': invalidModelId, limit: 50 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // Invalid model IDs should return empty array
      expect(response.body.items.length).toBe(0);

      console.log('✅ Invalid model ID correctly returns empty array');
    });

    it('should return content for valid model IDs only when filtering with mixed valid/invalid model IDs', async () => {
      console.log('\n📋 Test 6: Mixed valid and invalid model IDs');

      // This test will fail if model ID filtering is broken
      if (availableModelIds.length === 0) {
        throw new Error('No valid model IDs available for mixed valid/invalid filtering test. This indicates that the user content does not have model ID information.');
      }

      const validModelId = availableModelIds[0];
      const invalidModelId = 'invalid-model-id-67890';
      const testModelIds = [validModelId, invalidModelId];

      console.log(`🎯 Testing with valid ID: ${validModelId}`);
      console.log(`🎯 Testing with invalid ID: ${invalidModelId}`);

      const response = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ 'modelIds[]': testModelIds, limit: 50 })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // CRITICAL TEST: Mixed valid/invalid model ID filtering should return content for valid IDs only
      // Invalid IDs should be ignored, not cause the entire filter to fail
      expect(response.body.items.length).toBeGreaterThan(0);

      // Verify all returned items have the valid model ID (should ignore invalid one)
      response.body.items.forEach((item: any, index: number) => {
        let itemModelId = null;
        if (item.type === 'image' && item.imageCompletion?.modelId) {
          itemModelId = item.imageCompletion.modelId;
        } else if (item.type === 'video' && item.video?.modelId) {
          itemModelId = item.video.modelId;
        }

        console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
        expect(itemModelId).toBe(validModelId);
      });

      console.log('✅ Mixed valid/invalid model IDs filtering test passed');
    });
  });

  describe('Discovery Feed Model ID Filtering', () => {
    let discoveryBaselineResponse: any;
    let discoveryAvailableModelIds: string[] = [];

    it('should return discovery feed content when no modelIds parameter is provided (baseline)', async () => {
      console.log('\n📋 Discovery Test 1: Baseline (no filtering)');

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();
      expect(Array.isArray(response.body.items)).toBe(true);

      // Store baseline for comparison
      discoveryBaselineResponse = response.body;

      // Extract model IDs from the response for use in subsequent tests
      if (response.body.items && response.body.items.length > 0) {
        console.log('🔍 Extracting model IDs from discovery feed:');

        // Count content types
        const typeCounts = response.body.items.reduce((counts: any, item: any) => {
          counts[item.type] = (counts[item.type] || 0) + 1;
          return counts;
        }, {});
        console.log(`   Content type distribution: ${JSON.stringify(typeCounts)}`);

        response.body.items.forEach((item: any, index: number) => {
          let modelId = null;

          // Check for direct modelId field
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            modelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            modelId = item.video.modelId;
          }

          // Check for models array with first model's ID
          if (!modelId && item.type === 'image' && item.imageCompletion?.models?.length > 0) {
            modelId = item.imageCompletion.models[0].id;
          }

          if (modelId && !discoveryAvailableModelIds.includes(modelId)) {
            discoveryAvailableModelIds.push(modelId);
          }
        });

        console.log(`🎯 Available model IDs found in discovery feed: ${discoveryAvailableModelIds.length}`);
        console.log(`📝 Model IDs: ${discoveryAvailableModelIds.slice(0, 3).join(', ')}${discoveryAvailableModelIds.length > 3 ? '...' : ''}`);
      } else {
        console.log('⚠️  No items found in discovery baseline response');
      }

      console.log('✅ Discovery baseline test completed successfully');
    });

    it('should behave same as no parameter when modelIds[] is empty in discovery feed', async () => {
      console.log('\n📋 Discovery Test 2: Empty filter array');

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          'modelIds[]': '',
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);
      console.log(`📊 Baseline items: ${discoveryBaselineResponse?.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // Should return same amount as baseline (or similar, accounting for potential new content)
      const itemCountDiff = Math.abs((response.body.items?.length || 0) - (discoveryBaselineResponse?.items?.length || 0));
      console.log(`📈 Item count difference from baseline: ${itemCountDiff}`);

      // Allow small differences due to potential new content or cache updates
      expect(itemCountDiff).toBeLessThanOrEqual(10);
      console.log('✅ Discovery empty filter array test completed');
    });

    it('should return only content created with the specified model in discovery feed', async () => {
      console.log('\n📋 Discovery Test 3: Single valid model ID filtering');

      if (discoveryAvailableModelIds.length === 0) {
        console.log('⚠️  Skipping test - no model IDs available from discovery baseline');
        return;
      }

      const testModelId = discoveryAvailableModelIds[0];
      console.log(`🎯 Testing with model ID: ${testModelId}`);

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          'modelIds[]': testModelId,
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // CRITICAL TEST: Model ID filtering should return content with the specified model ID
      if (response.body.items.length > 0) {
        // Verify all returned items have the correct model ID
        let validItemsCount = 0;
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          } else if (item.type === 'image' && item.imageCompletion?.models?.length > 0) {
            itemModelId = item.imageCompletion.models[0].id;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          if (itemModelId === testModelId) {
            validItemsCount++;
          }
          expect(itemModelId).toBe(testModelId);
        });

        console.log(`✅ All ${validItemsCount} items have the correct model ID`);
      } else {
        console.log('⚠️  No items returned with the specified model ID');
        console.log('🔴 This indicates model ID filtering is not working for discovery feed');
      }

      console.log('✅ Discovery single model ID filtering test completed');
    });

    it('should return content created with any of the specified models in discovery feed', async () => {
      console.log('\n📋 Discovery Test 4: Multiple valid model IDs filtering');

      if (discoveryAvailableModelIds.length < 2) {
        console.log('⚠️  Skipping test - need at least 2 model IDs for multiple filtering test');
        return;
      }

      const testModelIds = discoveryAvailableModelIds.slice(0, 2);
      console.log(`🎯 Testing with model IDs: ${testModelIds.join(', ')}`);

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          'modelIds[]': testModelIds,
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // CRITICAL TEST: Multiple model ID filtering should return content with any of the specified model IDs
      if (response.body.items.length > 0) {
        // Verify all returned items have one of the specified model IDs
        let validItemsCount = 0;
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          } else if (item.type === 'image' && item.imageCompletion?.models?.length > 0) {
            itemModelId = item.imageCompletion.models[0].id;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          if (testModelIds.includes(itemModelId)) {
            validItemsCount++;
          }
          expect(testModelIds).toContain(itemModelId);
        });

        console.log(`✅ All ${validItemsCount} items have one of the specified model IDs`);
      } else {
        console.log('⚠️  No items returned with the specified model IDs');
        console.log('🔴 This indicates model ID filtering is not working for discovery feed');
      }

      console.log('✅ Discovery multiple model IDs filtering test completed');
    });

    it('should return empty array when filtering by invalid model IDs in discovery feed', async () => {
      console.log('\n📋 Discovery Test 5: Invalid/non-existent model IDs');

      const invalidModelId = 'invalid-discovery-model-id-99999';
      console.log(`🎯 Testing with invalid model ID: ${invalidModelId}`);

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          'modelIds[]': invalidModelId,
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // Invalid model IDs should return empty array
      expect(response.body.items.length).toBe(0);

      console.log('✅ Discovery invalid model ID correctly returns empty array');
    });

    it('should return content for valid model IDs only in discovery feed with mixed valid/invalid', async () => {
      console.log('\n📋 Discovery Test 6: Mixed valid and invalid model IDs');

      if (discoveryAvailableModelIds.length === 0) {
        console.log('⚠️  Skipping test - no valid model IDs available for mixed filtering test');
        return;
      }

      const validModelId = discoveryAvailableModelIds[0];
      const invalidModelId = 'invalid-discovery-model-id-88888';
      const testModelIds = [validModelId, invalidModelId];

      console.log(`🎯 Testing with valid ID: ${validModelId}`);
      console.log(`🎯 Testing with invalid ID: ${invalidModelId}`);

      const response = await request(baseHost)
        .get('/feed/discovery')
        .query({
          'modelIds[]': testModelIds,
          sortBy: 'createdAt',
          limit: 24,
          editedImages: false
        })
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log(`📊 Response status: ${response.status}`);
      console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

      // Validate response structure
      expect(response.body).toBeDefined();
      expect(response.body.items).toBeDefined();

      // Mixed valid/invalid should return content for valid IDs only
      if (response.body.items.length > 0) {
        // Verify all returned items have the valid model ID
        let validItemsCount = 0;
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          } else if (item.type === 'image' && item.imageCompletion?.models?.length > 0) {
            itemModelId = item.imageCompletion.models[0].id;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          if (itemModelId === validModelId) {
            validItemsCount++;
          }
          expect(itemModelId).toBe(validModelId);
        });

        console.log(`✅ All ${validItemsCount} items have the valid model ID (invalid ID was ignored)`);
      } else {
        console.log('⚠️  No items returned with the valid model ID');
        console.log('🔴 This may indicate model ID filtering is not working properly for discovery feed');
      }

      console.log('✅ Discovery mixed valid/invalid model IDs filtering test completed');
    });
  });

  afterAll(() => {
    console.log('\n🏁 Model ID Filtering E2E Tests Complete');
    console.log('');
    console.log('📊 Test Summary for /feed/user endpoint:');
    console.log('1. Baseline test: Validates basic feed functionality without filtering');
    console.log('2. Empty filter array test: Validates empty parameter handling');
    console.log('3. Single model ID test: Validates filtering by single model ID');
    console.log('4. Multiple model IDs test: Validates filtering by multiple model IDs (OR logic)');
    console.log('5. Invalid model ID test: Validates handling of invalid model IDs');
    console.log('6. Mixed valid/invalid test: Validates filtering with mixed valid/invalid IDs');
    console.log('');
    console.log('📊 Test Summary for /feed/discovery endpoint:');
    console.log('1. Discovery baseline test: Validates basic discovery feed without filtering');
    console.log('2. Discovery empty filter test: Validates empty parameter handling');
    console.log('3. Discovery single model ID test: Validates filtering by single model ID');
    console.log('4. Discovery multiple model IDs test: Validates filtering by multiple model IDs');
    console.log('5. Discovery invalid model ID test: Validates handling of invalid model IDs');
    console.log('6. Discovery mixed valid/invalid test: Validates filtering with mixed IDs');
    console.log('');
    console.log('💡 Note: Tests will fail if model ID filtering is not working correctly');
    console.log('📋 For debugging, monitor API logs:');
    console.log('   docker compose logs private-api | grep -E "(modelIds|feed)"');
  });
});
