import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Debug Database Content', () => {
  let app: INestApplication;
  let userToken: string;

  const baseHost = process.env.E2E_TEST_BASE_HOST || 'http://localhost:3000';
  const username = process.env.E2E_TEST_USERNAME || 'testuser';
  const password = process.env.E2E_TEST_PASSWORD || 'testpass';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Authenticate user
    const authResponse = await request(baseHost)
      .post('/auth/login')
      .send({
        username,
        password,
      })
      .expect(200);

    userToken = authResponse.body.jwtToken;
    console.log('✅ Authentication successful');
  });

  afterAll(async () => {
    await app.close();
  });

  it('should check database content for cursor pagination', async () => {
    console.log('🔍 Checking database content for cursor pagination...');

    // Get first page
    const page1Response = await request(baseHost)
      .get('/feed/discovery')
      .query({ limit: 10 }) // Get more items to see the full picture
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    console.log('📊 Page 1 Response (10 items):', {
      itemCount: page1Response.body.items.length,
      pagination: page1Response.body.pagination,
      items: page1Response.body.items.map((item, index) => ({
        index,
        type: item.type,
        id: item.id,
        score: item.score,
        createdAt: item.createdAt,
      })),
    });

    // Check if there are items with scores lower than the cursor
    const cursorScore = page1Response.body.pagination.nextCursor;
    const cursorTimestamp = page1Response.body.pagination.nextCursorTimestamp;
    
    console.log('🔍 Cursor Information:', {
      cursorScore,
      cursorTimestamp,
      lastItemScore: page1Response.body.items[page1Response.body.items.length - 1]?.score,
      lastItemCreatedAt: page1Response.body.items[page1Response.body.items.length - 1]?.createdAt,
    });

    // Get a larger page to see if there are more items
    const largePage = await request(baseHost)
      .get('/feed/discovery')
      .query({ limit: 50 }) // Get many more items
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    console.log('📊 Large Page Response (50 items):', {
      itemCount: largePage.body.items.length,
      pagination: largePage.body.pagination,
      scoreRange: {
        highest: largePage.body.items[0]?.score,
        lowest: largePage.body.items[largePage.body.items.length - 1]?.score,
      },
      itemsWithScoreLowerThanCursor: largePage.body.items.filter(item => 
        parseFloat(item.score) < cursorScore
      ).length,
    });

    // Show items that should be returned after the cursor
    const itemsAfterCursor = largePage.body.items.filter(item => {
      const itemScore = parseFloat(item.score);
      const itemCreatedAt = new Date(item.createdAt);
      const cursorDate = new Date(cursorTimestamp);
      
      // Items that should be returned: score < cursor OR (score = cursor AND createdAt < cursorTimestamp)
      return itemScore < cursorScore || (itemScore === cursorScore && itemCreatedAt < cursorDate);
    });

    console.log('🔍 Items that should be returned after cursor:', {
      count: itemsAfterCursor.length,
      items: itemsAfterCursor.slice(0, 5).map((item, index) => ({
        index,
        type: item.type,
        id: item.id,
        score: item.score,
        createdAt: item.createdAt,
      })),
    });

    expect(page1Response.body.items.length).toBeGreaterThan(0);
  });
});
