import * as request from 'supertest';

/**
 * Debug test to analyze cursor pagination behavior
 */
describe('Debug Cursor Pagination', () => {
  const baseHost = process.env.E2E_TEST_BASE_HOST;
  const testUsername = process.env.E2E_TEST_USERNAME;
  const testPassword = process.env.E2E_TEST_PASSWORD;

  let userToken: string;

  beforeAll(async () => {
    if (!baseHost || !testUsername || !testPassword) {
      throw new Error('Required environment variables missing');
    }

    // Authenticate
    const authResponse = await request(baseHost)
      .post('/auth/login')
      .send({
        username: testUsername,
        password: testPassword
      })
      .expect(200);

    userToken = authResponse.body.jwtToken;
    console.log('✅ Authentication successful');
  });

  it('should analyze cursor pagination behavior', async () => {
    console.log('🔍 Testing cursor pagination behavior...');
    
    // Get first page
    const page1Response = await request(baseHost)
      .get('/feed/discovery')
      .query({ limit: 3 })
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    console.log('📊 Page 1 Response:', {
      itemCount: page1Response.body.items.length,
      pagination: page1Response.body.pagination,
      items: page1Response.body.items.map((item: any, index: number) => ({
        index,
        type: item.type,
        id: item.type === 'image' ? item.imageCompletion?.id : item.video?.id,
        score: item.score,
        createdAt: item.type === 'image' ? item.imageCompletion?.createdAt : item.video?.createdAt
      }))
    });

    // Get second page using cursor
    if (page1Response.body.pagination.nextCursor) {
      const queryParams = {
        limit: 3,
        cursor: page1Response.body.pagination.nextCursor,
        cursorTimestamp: page1Response.body.pagination.nextCursorTimestamp
      };

      console.log('🔍 Page 2 Request Parameters:', queryParams);

      const page2Response = await request(baseHost)
        .get('/feed/discovery')
        .query(queryParams)
        .set('Authorization', `Bearer ${userToken}`)
        .expect((res) => {
          if (res.status !== 200) {
            console.log('❌ Page 2 Error Response:', {
              status: res.status,
              body: res.body,
              query: queryParams
            });
          }
          expect([200, 400]).toContain(res.status);
        });

      if (page2Response.status === 200) {
        console.log('📊 Page 2 Response:', {
          itemCount: page2Response.body.items.length,
          pagination: page2Response.body.pagination,
          items: page2Response.body.items.map((item: any, index: number) => ({
            index,
            type: item.type,
            id: item.type === 'image' ? item.imageCompletion?.id : item.video?.id,
            score: item.score,
            createdAt: item.type === 'image' ? item.imageCompletion?.createdAt : item.video?.createdAt
          }))
        });
      }

      // Check for overlaps only if page 2 was successful
      if (page2Response.status === 200) {
        const page1Ids = page1Response.body.items.map((item: any) =>
          item.type === 'image' ? item.imageCompletion?.id : item.video?.id
        );
        const page2Ids = page2Response.body.items.map((item: any) =>
          item.type === 'image' ? item.imageCompletion?.id : item.video?.id
        );

        const overlap = page1Ids.filter((id: string) => page2Ids.includes(id));

        console.log('🔍 Overlap Analysis:', {
          page1Ids,
          page2Ids,
          overlapIds: overlap,
          overlapCount: overlap.length
        });
      }

      console.log('🔍 Cursor Analysis:', {
        nextCursor: page1Response.body.pagination.nextCursor,
        nextCursorTimestamp: page1Response.body.pagination.nextCursorTimestamp,
        cursorType: typeof page1Response.body.pagination.nextCursor,
        cursorValue: page1Response.body.pagination.nextCursor,
        lastItemScore: page1Response.body.items[page1Response.body.items.length - 1]?.score,
        lastItemCreatedAt: page1Response.body.items[page1Response.body.items.length - 1]?.type === 'image' 
          ? page1Response.body.items[page1Response.body.items.length - 1]?.imageCompletion?.createdAt
          : page1Response.body.items[page1Response.body.items.length - 1]?.video?.createdAt
      });
    } else {
      console.log('❌ No nextCursor provided in page 1 response');
    }
  });
});
