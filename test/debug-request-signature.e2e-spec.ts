import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Debug Request Signature', () => {
  let app: INestApplication;
  let userToken: string;

  const baseHost = process.env.E2E_TEST_BASE_HOST || 'http://localhost:3000';
  const username = process.env.E2E_TEST_USERNAME || 'testuser';
  const password = process.env.E2E_TEST_PASSWORD || 'testpass';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Authenticate user
    const authResponse = await request(baseHost)
      .post('/auth/login')
      .send({
        username,
        password,
      })
      .expect(200);

    userToken = authResponse.body.jwtToken;
    console.log('✅ Authentication successful');
  });

  afterAll(async () => {
    await app.close();
  });

  it('should verify request signatures for cursor pagination', async () => {
    console.log('🔍 Testing request signatures for cursor pagination...');

    // Make first request without cursor
    const page1Response = await request(baseHost)
      .get('/feed/discovery')
      .query({ limit: 3 })
      .set('Authorization', `Bearer ${userToken}`)
      .expect(200);

    console.log('📊 Page 1 Response:', {
      itemCount: page1Response.body.items.length,
      pagination: page1Response.body.pagination,
      metadata: {
        requestSignature: page1Response.body.metadata?.requestSignature,
        generatedAt: page1Response.body.metadata?.generatedAt,
        cacheStatus: page1Response.body.metadata?.cacheStatus,
      },
    });

    // Make second request with cursor
    if (page1Response.body.pagination.nextCursor) {
      const queryParams = {
        limit: 3,
        cursor: page1Response.body.pagination.nextCursor,
        cursorTimestamp: page1Response.body.pagination.nextCursorTimestamp
      };

      console.log('🔍 Page 2 Request Parameters:', queryParams);

      const page2Response = await request(baseHost)
        .get('/feed/discovery')
        .query(queryParams)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      console.log('📊 Page 2 Response:', {
        itemCount: page2Response.body.items.length,
        pagination: page2Response.body.pagination,
        metadata: {
          requestSignature: page2Response.body.metadata?.requestSignature,
          generatedAt: page2Response.body.metadata?.generatedAt,
          cacheStatus: page2Response.body.metadata?.cacheStatus,
        },
      });

      // Compare request signatures
      const page1Signature = page1Response.body.metadata?.requestSignature;
      const page2Signature = page2Response.body.metadata?.requestSignature;

      console.log('🔍 Signature Analysis:', {
        page1Signature,
        page2Signature,
        signaturesAreDifferent: page1Signature !== page2Signature,
        page1GeneratedAt: page1Response.body.metadata?.generatedAt,
        page2GeneratedAt: page2Response.body.metadata?.generatedAt,
        generationTimesAreDifferent: page1Response.body.metadata?.generatedAt !== page2Response.body.metadata?.generatedAt,
      });

      // Check if responses are identical (indicating caching)
      const page1Items = page1Response.body.items.map(item => ({ id: item.id, score: item.score }));
      const page2Items = page2Response.body.items.map(item => ({ id: item.id, score: item.score }));
      const responsesAreIdentical = JSON.stringify(page1Items) === JSON.stringify(page2Items);

      console.log('🔍 Caching Analysis:', {
        responsesAreIdentical,
        page1Items: page1Items,
        page2Items: page2Items,
        possibleCachingIssue: responsesAreIdentical && page1Signature === page2Signature,
      });

      // If signatures are the same but requests are different, it indicates caching
      if (page1Signature === page2Signature) {
        console.log('⚠️  WARNING: Request signatures are identical despite different parameters - this indicates HTTP-level caching');
      } else {
        console.log('✅ Request signatures are different - requests are being processed correctly');
      }
    }

    expect(page1Response.body.items.length).toBeGreaterThan(0);
  });
});
