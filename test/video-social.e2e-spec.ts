import * as request from 'supertest';

describe('Video Social Integration (e2e)', () => {
  // Test configuration
  const baseHost = process.env.E2E_TEST_BASE_HOST || 'https://private-api.letzai.gonser.com.br';
  const testUsername = process.env.E2E_TEST_USERNAME || 'mischstrotz';
  const testPassword = process.env.E2E_TEST_PASSWORD || 'L3tzAI@2023!';

  // Test data - using real data from the API
  let userToken: string;
  let userId: string;
  let testVideoId: string;
  let testCommentId: string;

  beforeAll(async () => {
    console.log('🔧 Setting up Video Social E2E Tests');
    console.log(`📍 API Host: ${baseHost}`);
    console.log(`👤 Test User: ${testUsername}`);

    // Authenticate and get user token
    console.log('🔐 Authenticating...');
    const authResponse = await request(baseHost)
      .post('/auth/login')
      .send({
        username: testUsername,
        password: testPassword,
      });

    if (authResponse.status !== 200) {
      throw new Error(`Authentication failed: ${authResponse.status} - ${JSON.stringify(authResponse.body)}`);
    }

    userToken = authResponse.body.jwtToken;
    userId = authResponse.body.user?.id || 'unknown';
    console.log('✅ Authentication successful');

    // Get a test video from the user's content that's ready for interaction
    console.log('🎥 Finding test video...');
    const videosResponse = await request(baseHost)
      .get('/videos')
      .query({ limit: 10, status: 'ready' }) // Look for ready videos specifically
      .set('Authorization', `Bearer ${userToken}`);

    if (videosResponse.status === 200 && videosResponse.body?.length > 0) {
      // Find a video that's ready for interaction
      const readyVideo = videosResponse.body.find(video =>
        video.status === 'ready'
      );

      if (readyVideo) {
        testVideoId = readyVideo.id;
        console.log(`✅ Found ready test video: ${testVideoId} (status: ${readyVideo.status})`);
      } else {
        console.log('⚠️  No ready videos found for interaction');
        console.log(`📊 Available videos:`, videosResponse.body.map(v => ({
          id: v.id,
          status: v.status,
          privacy: v.privacy
        })));
        testVideoId = null;
      }
    } else {
      console.log('⚠️  No videos found, some tests may be skipped');
      console.log(`📊 Videos response status: ${videosResponse.status}`);
      console.log(`📊 Videos response body:`, videosResponse.body);
      testVideoId = null;
    }
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up Video Social E2E Tests');
    
    // Clean up any comments we created
    if (testCommentId && testVideoId) {
      try {
        await request(baseHost)
          .delete(`/social/video/${testVideoId}/comments/${testCommentId}`)
          .set('Authorization', `Bearer ${userToken}`);
        console.log('✅ Cleaned up test comment');
      } catch (error) {
        console.log('⚠️  Could not clean up test comment');
      }
    }
  });

  beforeEach(async () => {
    // Reset test state
    testCommentId = null;
  });

  describe('Video Comments', () => {
    it('should create a comment on a video', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping comment test - no test video available');
        return;
      }

      const response = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          comment: 'This is a great video!',
        });

      if (response.status !== 201) {
        console.log(`❌ Comment creation failed with status ${response.status}`);
        console.log(`📄 Response body:`, response.body);
        console.log(`📄 Response text:`, response.text);
      }

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.comment).toBe('This is a great video!');
      expect(response.body.entityType).toBe('video');
      expect(response.body.entityId).toBe(testVideoId);
      expect(response.body.user).toBeDefined();
      
      // Store comment ID for cleanup
      testCommentId = response.body.id;
    });

    it('should get comments for a video', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping get comments test - no test video available');
        return;
      }

      const response = await request(baseHost)
        .get(`/social/video/${testVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Comments array can be empty or contain comments
    });

    it('should delete own comment', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping delete comment test - no test video available');
        return;
      }

      // First create a comment
      const createResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          comment: 'Comment to be deleted',
        });

      expect(createResponse.status).toBe(201);
      const commentId = createResponse.body.id;

      // Then delete it
      const deleteResponse = await request(baseHost)
        .delete(`/social/video/${testVideoId}/comments/${commentId}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(deleteResponse.status).toBe(204);
    });
  });

  describe('Video Likes', () => {
    it('should like a video', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping like test - no test video available');
        return;
      }

      // First, try to unlike the video in case it's already liked
      await request(baseHost)
        .delete(`/social/video/${testVideoId}/likes`)
        .set('Authorization', `Bearer ${userToken}`);

      // Now like the video
      const response = await request(baseHost)
        .post(`/social/video/${testVideoId}/likes`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.entityType).toBe('video');
      expect(response.body.entityId).toBe(testVideoId);
    });

    it('should unlike a video', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping unlike test - no test video available');
        return;
      }

      // First like the video
      const likeResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/likes`)
        .set('Authorization', `Bearer ${userToken}`);

      if (likeResponse.status === 201) {
        // Then unlike it
        const unlikeResponse = await request(baseHost)
          .delete(`/social/video/${testVideoId}/likes`)
          .set('Authorization', `Bearer ${userToken}`);

        expect(unlikeResponse.status).toBe(200);
      }
    });

    it('should get likes for a video', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping get likes test - no test video available');
        return;
      }

      const response = await request(baseHost)
        .get(`/social/video/${testVideoId}/likes`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Likes array can be empty or contain likes
    });
  });

  describe('Authentication & Authorization', () => {
    it('should require authentication for social endpoints', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping auth test - no test video available');
        return;
      }

      // Test comment creation without auth
      const commentResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .send({
          comment: 'This should fail',
        });

      expect(commentResponse.status).toBe(401);

      // Test like creation without auth
      const likeResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/likes`);

      expect(likeResponse.status).toBe(401);
    });

    it('should reject invalid JWT tokens', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping invalid token test - no test video available');
        return;
      }

      const response = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .set('Authorization', 'Bearer invalid-token')
        .send({
          comment: 'This should fail',
        });

      expect(response.status).toBe(401);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid video IDs', async () => {
      const invalidVideoId = '00000000-0000-0000-0000-000000000000';

      const response = await request(baseHost)
        .post(`/social/video/${invalidVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          comment: 'This should fail',
        });

      expect(response.status).toBe(404);
    });

    it('should validate comment content', async () => {
      if (!testVideoId) {
        console.log('⚠️  Skipping validation test - no test video available');
        return;
      }

      // Test empty comment
      const emptyResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          comment: '',
        });

      expect(emptyResponse.status).toBe(400);

      // Test missing comment
      const missingResponse = await request(baseHost)
        .post(`/social/video/${testVideoId}/comments`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({});

      expect(missingResponse.status).toBe(400);
    });
  });
});
