import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from './../src/app.module';

describe('User Feed Endpoint (Comprehensive E2E)', () => {
  let app: INestApplication;
  let baseHost: string;
  let userToken: string;
  let testUserIdentifier: string;

  // Helper function to validate feed response structure
  function validateFeedResponse(body: any) {
    expect(body).toHaveProperty('items');
    expect(body).toHaveProperty('pagination');
    expect(body).toHaveProperty('metadata');
    expect(Array.isArray(body.items)).toBe(true);

    // Validate pagination structure
    expect(body.pagination).toHaveProperty('count');
    expect(body.pagination).toHaveProperty('hasMore');
    expect(typeof body.pagination.count).toBe('number');
    expect(typeof body.pagination.hasMore).toBe('boolean');

    // Validate metadata structure
    expect(body.metadata).toHaveProperty('feedType');
    expect(body.metadata).toHaveProperty('generatedAt');
    expect(body.metadata.feedType).toBe('user');
    expect(typeof body.metadata.generatedAt).toBe('string');

    // Validate each item structure
    body.items.forEach((item: any) => {
      expect(item).toHaveProperty('type');
      expect(['image', 'video']).toContain(item.type);

      if (item.type === 'image') {
        expect(item).toHaveProperty('imageCompletion');
        expect(item.imageCompletion).toHaveProperty('id');
        expect(item.imageCompletion).toHaveProperty('createdAt');
        expect(item.imageCompletion).toHaveProperty('privacy');
      } else if (item.type === 'video') {
        expect(item).toHaveProperty('video');
        expect(item.video).toHaveProperty('id');
        expect(item.video).toHaveProperty('createdAt');
        expect(item.video).toHaveProperty('privacy');
      }
    });
  }

  // Helper function to validate that all items belong to the specified user
  function validateUserOwnership(body: any, expectedUserIdentifier: string, expectedUserId?: string) {
    expect(body).toHaveProperty('items');
    expect(Array.isArray(body.items)).toBe(true);

    body.items.forEach((item: any, index: number) => {
      let itemUser = null;
      let itemUserId = null;
      let itemUsername = null;

      if (item.type === 'image' && item.imageCompletion) {
        itemUser = item.imageCompletion.user;
        itemUserId = item.imageCompletion.userId;
        itemUsername = item.imageCompletion.username;
      } else if (item.type === 'video' && item.video) {
        itemUser = item.video.user;
        itemUserId = item.video.userId;
        itemUsername = item.video.username;
      }

      // Validate that user information is present
      expect(itemUser).toBeDefined();
      expect(itemUser).toHaveProperty('id');
      expect(itemUser).toHaveProperty('username');

      // Validate ownership by checking user identifier (username or userId)
      const isValidOwnership =
        itemUser.username === expectedUserIdentifier ||
        itemUser.id === expectedUserIdentifier ||
        (expectedUserId && itemUser.id === expectedUserId) ||
        (expectedUserId && itemUserId === expectedUserId) ||
        itemUsername === expectedUserIdentifier;

      if (!isValidOwnership) {
        console.error(`❌ Item ${index + 1} ownership validation failed:`);
        console.error(`   Expected user: ${expectedUserIdentifier} ${expectedUserId ? `(ID: ${expectedUserId})` : ''}`);
        console.error(`   Actual user: ${itemUser.username} (ID: ${itemUser.id})`);
        console.error(`   Item type: ${item.type}`);
        console.error(`   Item ID: ${item.type === 'image' ? item.imageCompletion?.id : item.video?.id}`);
      }

      expect(isValidOwnership).toBe(true);
    });
  }

  beforeAll(async () => {
    // Use environment variables for configuration
    baseHost = process.env.E2E_TEST_BASE_HOST || 'http://localhost:3000';
    const username = process.env.E2E_TEST_USERNAME;
    const password = process.env.E2E_TEST_PASSWORD;

    if (!username || !password) {
      throw new Error('E2E_TEST_USERNAME and E2E_TEST_PASSWORD environment variables are required');
    }

    console.log('Authenticating against API:', baseHost);
    console.log('Using username:', username);

    // Authenticate and get JWT token
    const authResponse = await request(baseHost)
      .post('/auth/login')
      .send({
        username: username,
        password: password,
      })
      .expect(200);

    expect(authResponse.body).toHaveProperty('jwtToken');
    userToken = authResponse.body.jwtToken;
    
    console.log('✅ Authentication successful - JWT token obtained');
    console.log('Token preview:', userToken.substring(0, 50) + '...');

    // Set test user identifier (we'll use the authenticated user's identifier)
    // For now, we'll use a known test user identifier - this should be configurable
    testUserIdentifier = username; // or could be a separate env var like E2E_TEST_TARGET_USER
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('User Feed Privacy Scenarios', () => {
    describe('Privacy=private (Own Content)', () => {
      it('should return private content when viewing own profile with authentication', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'private', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        validateFeedResponse(response.body);

        // Verify that private content is included
        response.body.items.forEach((item: any) => {
          const privacy = item.type === 'image' 
            ? item.imageCompletion?.privacy 
            : item.video?.privacy;
          
          // Should include private content when viewing own profile
          expect(['private', 'public']).toContain(privacy);
        });

        // Should have some content (assuming test user has created content)
        expect(response.body.items.length).toBeGreaterThanOrEqual(0);
      });

      it('should return empty or limited results when viewing private content of another user', async () => {
        // This test assumes we have another user identifier to test with
        // For now, we'll test the behavior - this might return 403 or empty results
        const otherUserIdentifier = 'testuser2'; // This should be configurable
        
        const response = await request(baseHost)
          .get(`/feed/user/${otherUserIdentifier}`)
          .query({ privacy: 'private', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`);

        // The response could be 403 Forbidden, 404 Not Found, or 200 with empty results
        // depending on the API implementation
        if (response.status === 200) {
          validateFeedResponse(response.body);
          // Should not return private content of other users
          response.body.items.forEach((item: any) => {
            const privacy = item.type === 'image'
              ? item.imageCompletion?.privacy
              : item.video?.privacy;

            // Should not include private content when viewing another user's profile
            expect(privacy).not.toBe('private');
          });
        } else {
          // Expect either 403 (Forbidden) or 404 (Not Found)
          expect([403, 404]).toContain(response.status);
        }
      });

      it('should require authentication for private content access', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'private', limit: 10 });

        // Should either require authentication (401) or return only public content
        if (response.status === 401) {
          expect(response.status).toBe(401);
        } else if (response.status === 200) {
          validateFeedResponse(response.body);
          // If it returns 200, it should only contain public content
          response.body.items.forEach((item: any) => {
            const privacy = item.type === 'image' 
              ? item.imageCompletion?.privacy 
              : item.video?.privacy;
            expect(privacy).toBe('public');
          });
        }
      });
    });

    describe('Privacy=public (Public Content)', () => {
      it('should return public content with authentication', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'public', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        validateFeedResponse(response.body);

        // Verify that only public content is returned
        response.body.items.forEach((item: any) => {
          const privacy = item.type === 'image'
            ? item.imageCompletion?.privacy
            : item.video?.privacy;

          expect(privacy).toBe('public');
        });
      });

      it('should return public content without authentication', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'public', limit: 10 })
          .expect(200);

        validateFeedResponse(response.body);

        // Verify that only public content is returned
        response.body.items.forEach((item: any) => {
          const privacy = item.type === 'image' 
            ? item.imageCompletion?.privacy 
            : item.video?.privacy;
          
          expect(privacy).toBe('public');
        });
      });

      it('should return same public content for authenticated and unauthenticated requests', async () => {
        // Get public content with authentication
        const authenticatedResponse = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'public', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        // Get public content without authentication
        const unauthenticatedResponse = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'public', limit: 10 })
          .expect(200);

        validateFeedResponse(authenticatedResponse.body);
        validateFeedResponse(unauthenticatedResponse.body);

        // Both should return the same public content
        expect(authenticatedResponse.body.items.length).toBe(unauthenticatedResponse.body.items.length);
        
        // Verify content is identical (same IDs in same order)
        for (let i = 0; i < authenticatedResponse.body.items.length; i++) {
          const authItem = authenticatedResponse.body.items[i];
          const unauthItem = unauthenticatedResponse.body.items[i];
          
          const authId = authItem.type === 'image' 
            ? authItem.imageCompletion?.id 
            : authItem.video?.id;
          const unauthId = unauthItem.type === 'image' 
            ? unauthItem.imageCompletion?.id 
            : unauthItem.video?.id;
            
          expect(authId).toBe(unauthId);
        }
      });
    });

    describe('Privacy=all (All Accessible Content)', () => {
      it('should return all accessible content when viewing own profile with authentication', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'all', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        validateFeedResponse(response.body);

        // Should include both private and public content when viewing own profile
        const privacyValues = response.body.items.map((item: any) => 
          item.type === 'image' 
            ? item.imageCompletion?.privacy 
            : item.video?.privacy
        );

        // Should contain both private and public content (if user has both types)
        const hasPrivate = privacyValues.includes('private');
        const hasPublic = privacyValues.includes('public');
        
        // All privacy values should be either 'private' or 'public'
        privacyValues.forEach((privacy: string) => {
          expect(['private', 'public']).toContain(privacy);
        });

        // Log for debugging
        console.log('Privacy distribution in "all" results:', {
          total: privacyValues.length,
          private: privacyValues.filter(p => p === 'private').length,
          public: privacyValues.filter(p => p === 'public').length,
        });
      });

      it('should return only public content when viewing another user with privacy=all', async () => {
        const otherUserIdentifier = 'testuser2'; // This should be configurable
        
        const response = await request(baseHost)
          .get(`/feed/user/${otherUserIdentifier}`)
          .query({ privacy: 'all', limit: 10 })
          .set('Authorization', `Bearer ${userToken}`);

        if (response.status === 200) {
          validateFeedResponse(response.body);

          // Should only return public content when viewing another user's profile
          response.body.items.forEach((item: any) => {
            const privacy = item.type === 'image'
              ? item.imageCompletion?.privacy
              : item.video?.privacy;

            expect(privacy).toBe('public');
          });
        } else {
          // Expect either 403 (Forbidden) or 404 (Not Found)
          expect([403, 404]).toContain(response.status);
        }
      });

      it('should return only public content when accessing privacy=all without authentication', async () => {
        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ privacy: 'all', limit: 10 });

        if (response.status === 200) {
          validateFeedResponse(response.body);
          
          // Should only return public content without authentication
          response.body.items.forEach((item: any) => {
            const privacy = item.type === 'image' 
              ? item.imageCompletion?.privacy 
              : item.video?.privacy;
            
            expect(privacy).toBe('public');
          });
        } else {
          // Might require authentication
          expect(response.status).toBe(401);
        }
      });
    });

    describe('Privacy=all Comprehensive Validation', () => {
      it('should return combined private and public content when privacy=all for own profile', async () => {
        console.log('🔍 Starting comprehensive privacy=all validation test...');

        // Helper function to collect all items with pagination
        async function collectAllItems(privacy: string): Promise<any[]> {
          const allItems: any[] = [];
          const seenIds = new Set<string>(); // Track seen IDs to detect duplicates
          let cursor: number | undefined;
          let hasMore = true;
          let pageCount = 0;
          const maxPages = 10; // Reduced safety limit
          const maxItems = 500; // Maximum items to collect per privacy setting

          while (hasMore && pageCount < maxPages && allItems.length < maxItems) {
            pageCount++;
            console.log(`📄 Fetching page ${pageCount} for privacy=${privacy}...`);

            const queryParams: any = {
              privacy,
              limit: 50 // Reduced limit for faster testing
            };

            if (cursor !== undefined) {
              queryParams.cursor = cursor;
            }

            const response = await request(baseHost)
              .get(`/feed/user/${testUserIdentifier}`)
              .query(queryParams)
              .set('Authorization', `Bearer ${userToken}`);

            if (response.status !== 200) {
              console.log(`❌ Error on page ${pageCount} for privacy=${privacy}:`);
              console.log(`   Status: ${response.status}`);
              console.log(`   Query params:`, queryParams);
              console.log(`   Response body:`, response.body);
              throw new Error(`Request failed with status ${response.status}: ${JSON.stringify(response.body)}`);
            }

            validateFeedResponse(response.body);

            // Check for duplicate items to detect pagination issues
            let duplicateCount = 0;
            const newItems: any[] = [];

            for (const item of response.body.items) {
              const itemId = item.type === 'image'
                ? `image-${item.imageCompletion?.id}`
                : `video-${item.video?.id}`;

              if (seenIds.has(itemId)) {
                duplicateCount++;
              } else {
                seenIds.add(itemId);
                newItems.push(item);
              }
            }

            // Add only new items to collection
            allItems.push(...newItems);

            // Update pagination info
            hasMore = response.body.pagination.hasMore;
            cursor = response.body.pagination.nextCursor;

            console.log(`📊 Page ${pageCount} results: ${response.body.items.length} items, ${newItems.length} new, ${duplicateCount} duplicates, hasMore: ${hasMore}`);

            // Safety checks to prevent infinite loops
            if (response.body.items.length === 0) {
              console.log('⚠️ No items returned, breaking pagination loop');
              break;
            }

            if (newItems.length === 0 && duplicateCount > 0) {
              console.log('⚠️ All items were duplicates, possible pagination issue, breaking loop');
              break;
            }

            if (duplicateCount > response.body.items.length * 0.5) {
              console.log('⚠️ High duplicate rate detected, possible pagination issue');
            }
          }

          console.log(`✅ Collected ${allItems.length} total items for privacy=${privacy} across ${pageCount} pages`);
          return allItems;
        }

        // Helper function to extract unique item IDs
        function extractItemIds(items: any[]): Set<string> {
          const ids = items.map((item: any) => {
            if (item.type === 'image') {
              return `image-${item.imageCompletion?.id}`;
            } else if (item.type === 'video') {
              return `video-${item.video?.id}`;
            } else {
              return `unknown-${item.entityId || 'no-id'}`;
            }
          }).filter(id => id && !id.includes('undefined'));

          return new Set(ids);
        }

        // Helper function to analyze privacy distribution
        function analyzePrivacyDistribution(items: any[]): { private: number; public: number; total: number } {
          let privateCount = 0;
          let publicCount = 0;

          items.forEach((item: any) => {
            const privacy = item.type === 'image'
              ? item.imageCompletion?.privacy
              : item.video?.privacy;

            if (privacy === 'private') {
              privateCount++;
            } else if (privacy === 'public') {
              publicCount++;
            }
          });

          return {
            private: privateCount,
            public: publicCount,
            total: items.length
          };
        }

        // Collect all items for each privacy setting
        console.log('🔄 Collecting items for privacy=private...');
        const privateItems = await collectAllItems('private');

        console.log('🔄 Collecting items for privacy=public...');
        const publicItems = await collectAllItems('public');

        console.log('🔄 Collecting items for privacy=all...');
        const allItems = await collectAllItems('all');

        // Extract item IDs for set operations
        const privateIds = extractItemIds(privateItems);
        const publicIds = extractItemIds(publicItems);
        const allIds = extractItemIds(allItems);

        // Analyze privacy distribution in each result set
        const privateDistribution = analyzePrivacyDistribution(privateItems);
        const publicDistribution = analyzePrivacyDistribution(publicItems);
        const allDistribution = analyzePrivacyDistribution(allItems);

        // Log comprehensive results
        console.log('📊 COMPREHENSIVE PRIVACY ANALYSIS:');
        console.log('=====================================');
        console.log(`🔒 privacy=private: ${privateItems.length} items`);
        console.log(`   - Private content: ${privateDistribution.private}`);
        console.log(`   - Public content: ${privateDistribution.public}`);
        console.log(`   - Unique IDs: ${privateIds.size}`);

        console.log(`🌍 privacy=public: ${publicItems.length} items`);
        console.log(`   - Private content: ${publicDistribution.private}`);
        console.log(`   - Public content: ${publicDistribution.public}`);
        console.log(`   - Unique IDs: ${publicIds.size}`);

        console.log(`🔄 privacy=all: ${allItems.length} items`);
        console.log(`   - Private content: ${allDistribution.private}`);
        console.log(`   - Public content: ${allDistribution.public}`);
        console.log(`   - Unique IDs: ${allIds.size}`);

        // Perform set operations for validation
        const privateOnlyIds = new Set([...privateIds].filter(id => !publicIds.has(id)));
        const publicOnlyIds = new Set([...publicIds].filter(id => !privateIds.has(id)));
        const overlapIds = new Set([...privateIds].filter(id => publicIds.has(id)));

        console.log('🔍 SET ANALYSIS:');
        console.log('================');
        console.log(`Private-only items: ${privateOnlyIds.size}`);
        console.log(`Public-only items: ${publicOnlyIds.size}`);
        console.log(`Overlap items: ${overlapIds.size}`);
        console.log(`Expected total for privacy=all: ${privateIds.size + publicIds.size - overlapIds.size}`);
        console.log(`Actual total for privacy=all: ${allIds.size}`);

        // CRITICAL VALIDATIONS
        console.log('🧪 PERFORMING VALIDATIONS:');
        console.log('===========================');

        // 1. Validate that privacy=private only returns private content (if any exists)
        console.log('✅ Validating privacy=private results...');
        if (privateDistribution.private > 0) {
          expect(privateDistribution.public).toBe(0); // Should have NO public content
          console.log(`   ✓ privacy=private correctly returns only private content (${privateDistribution.private} items)`);
        } else {
          console.log(`   ⚠️  User has no private content - skipping private content validation`);
        }

        // 2. Validate that privacy=public only returns public content
        console.log('✅ Validating privacy=public results...');
        expect(publicDistribution.public).toBeGreaterThan(0); // Should have some public content
        expect(publicDistribution.private).toBe(0); // Should have NO private content
        console.log(`   ✓ privacy=public correctly returns only public content (${publicDistribution.public} items)`);

        // 3. CRITICAL TEST: Validate that privacy=all returns appropriate content
        console.log('🔥 CRITICAL TEST: Validating privacy=all results...');

        // Check if privacy=all contains private content
        const hasPrivateInAll = allDistribution.private > 0;
        const hasPublicInAll = allDistribution.public > 0;

        console.log(`   Private content in privacy=all: ${allDistribution.private} items`);
        console.log(`   Public content in privacy=all: ${allDistribution.public} items`);

        // Determine expected behavior based on available content
        const hasPrivateContent = privateDistribution.private > 0;
        const hasPublicContent = publicDistribution.public > 0;

        if (hasPrivateContent && !hasPrivateInAll) {
          console.log('❌ CRITICAL FAILURE: privacy=all does NOT contain private content!');
          console.log('   This confirms the bug - privacy=all should include private content for own profile');
        } else if (!hasPrivateContent) {
          console.log('   ℹ️  User has no private content - privacy=all should only contain public content');
        }

        if (hasPublicContent && !hasPublicInAll) {
          console.log('❌ CRITICAL FAILURE: privacy=all does NOT contain public content!');
        }

        // 4. Pagination-aware validation
        console.log('🧮 Pagination-aware validation...');
        console.log(`   privacy=all returned ${allItems.length} items (limited by pagination)`);
        console.log(`   Content mix: ${allDistribution.private} private + ${allDistribution.public} public`);

        // Validate that privacy=all items are a subset of the union of private + public
        const allItemsAreValid = [...allIds].every(id => privateIds.has(id) || publicIds.has(id));
        console.log(`   All privacy=all items are valid (from private or public sets): ${allItemsAreValid}`);

        // Check for any unexpected items
        const extraInAll = new Set([...allIds].filter(id => !privateIds.has(id) && !publicIds.has(id)));
        console.log(`   Unexpected items in privacy=all: ${extraInAll.size}`);

        // ASSERTIONS - These will determine if the test passes or fails
        console.log('🎯 FINAL ASSERTIONS:');
        console.log('====================');

        // Basic sanity checks - at least one type of content should exist
        expect(publicItems.length).toBeGreaterThan(0); // User should have some public content

        // Privacy filtering validation
        if (privateItems.length > 0) {
          expect(privateDistribution.public).toBe(0); // privacy=private should have no public content
        }
        expect(publicDistribution.private).toBe(0); // privacy=public should have no private content

        // THE CRITICAL TEST: privacy=all should contain appropriate content based on what exists
        if (hasPrivateContent) {
          expect(hasPrivateInAll).toBe(true); // CRITICAL: privacy=all MUST include private content if it exists
        }
        if (hasPublicContent) {
          expect(hasPublicInAll).toBe(true);  // CRITICAL: privacy=all MUST include public content if it exists
        }

        // privacy=all should return SOME content (either private, public, or both)
        expect(allItems.length).toBeGreaterThan(0); // CRITICAL: privacy=all should not be empty

        // Pagination-aware validation (instead of expecting exact mathematical union)
        expect(allItemsAreValid).toBe(true); // All items in privacy=all should be from private or public sets
        expect(extraInAll.size).toBe(0); // No unexpected items should be in privacy=all

        // Ensure privacy=all returns a reasonable mix of content (not just one type)
        const privateRatio = allDistribution.private / allItems.length;
        const publicRatio = allDistribution.public / allItems.length;
        console.log(`   Content ratios: ${(privateRatio * 100).toFixed(1)}% private, ${(publicRatio * 100).toFixed(1)}% public`);

        // Both ratios should be > 0 (we have both types) and < 1 (not exclusively one type)
        expect(privateRatio).toBeGreaterThan(0);
        expect(privateRatio).toBeLessThan(1);
        expect(publicRatio).toBeGreaterThan(0);
        expect(publicRatio).toBeLessThan(1);

        console.log('🎉 ALL VALIDATIONS PASSED! privacy=all is working correctly with proper content mixing!');
      }, 180000); // 3 minute timeout for comprehensive test
    });
  });

  describe('User Ownership Validation', () => {
    let targetUserId: string | undefined;

    beforeAll(async () => {
      // Try to get the actual user ID from the authentication response
      try {
        const authResponse = await request(baseHost)
          .post('/auth/login')
          .send({
            username: process.env.E2E_TEST_USERNAME,
            password: process.env.E2E_TEST_PASSWORD,
          });

        if (authResponse.body.user?.id) {
          targetUserId = authResponse.body.user.id;
          console.log(`🎯 Target user ID obtained: ${targetUserId}`);
        } else {
          console.log('⚠️  Could not obtain user ID from auth response, will use username-based validation');
        }
      } catch (error) {
        console.log('⚠️  Could not re-authenticate to get user ID, will use username-based validation');
      }
    });

    it('should return only content that belongs to the specified user across ALL pages', async () => {
      console.log('\n🔍 Testing User Ownership Validation Across ALL Pages');
      console.log('=====================================================');
      console.log(`📋 Target user identifier: ${testUserIdentifier}`);
      console.log(`📋 Target user ID: ${targetUserId || 'Not available'}`);

      // First, get the first page to understand the total scope
      const firstPageResponse = await request(baseHost)
        .get(`/feed/user/${testUserIdentifier}`)
        .query({ privacy: 'all', limit: 20 }) // Use smaller pages for thorough testing
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      validateFeedResponse(firstPageResponse.body);

      if (firstPageResponse.body.items.length === 0) {
        console.log('⚠️  No items returned - cannot validate ownership (user may have no content)');
        return;
      }

      // Collect all items from all pages using cursor pagination
      console.log('\n📄 Collecting items from ALL pages...');
      let allItems: any[] = [];
      let currentCursor: string | undefined = undefined;
      let pageCount = 0;
      let hasMore = true;
      const maxPages = 10; // Reasonable limit for E2E testing (200 items total)

      while (hasMore && pageCount < maxPages) {
        pageCount++;
        console.log(`   📄 Fetching page ${pageCount}...`);

        const pageResponse = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({
            privacy: 'all',
            limit: 20,
            ...(currentCursor ? { cursor: currentCursor } : {})
          })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        const pageItems = pageResponse.body.items || [];
        console.log(`      📦 Items on page ${pageCount}: ${pageItems.length}`);

        if (pageItems.length === 0) {
          console.log(`      ⚠️  No items on page ${pageCount}, stopping pagination`);
          break;
        }

        allItems = allItems.concat(pageItems);

        // Check if there are more pages
        hasMore = pageResponse.body.pagination?.hasMore === true;
        currentCursor = pageResponse.body.pagination?.nextCursor;

        if (!hasMore) {
          console.log(`      ✅ Reached last page (page ${pageCount})`);
          break;
        }

        if (!currentCursor) {
          console.log(`      ⚠️  No cursor for next page, stopping pagination`);
          break;
        }

        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`\n📊 COMPLETE DATASET COLLECTED:`);
      console.log(`   📄 Total pages fetched: ${pageCount}`);
      console.log(`   📦 Total items collected: ${allItems.length}`);

      if (pageCount >= maxPages) {
        console.log(`   ⚠️  Reached maximum page limit (${maxPages}) - there may be more content`);
      }

      // Create a mock response body with all items for validation
      const completeResponse = {
        items: allItems,
        pagination: {
          count: allItems.length,
          hasMore: false
        }
      };

      // Validate that ALL items belong to the correct user
      validateUserOwnership(completeResponse, testUserIdentifier, targetUserId);

      // Additional detailed logging for verification across ALL pages
      console.log('\n📋 Detailed ownership analysis across ALL pages:');
      const ownershipStats = {
        totalItems: allItems.length,
        imageItems: 0,
        videoItems: 0,
        uniqueUserIds: new Set<string>(),
        uniqueUsernames: new Set<string>(),
        pageBreakdown: [] as Array<{page: number, items: number, images: number, videos: number}>,
      };

      // Analyze items page by page for detailed breakdown
      let currentPageItems = 0;
      let currentPageImages = 0;
      let currentPageVideos = 0;
      const itemsPerPage = 20;

      allItems.forEach((item: any, index: number) => {
        let itemUser = null;
        let itemUserId = null;
        let itemUsername = null;

        if (item.type === 'image') {
          ownershipStats.imageItems++;
          currentPageImages++;
          itemUser = item.imageCompletion?.user;
          itemUserId = item.imageCompletion?.userId;
          itemUsername = item.imageCompletion?.username;
        } else if (item.type === 'video') {
          ownershipStats.videoItems++;
          currentPageVideos++;
          itemUser = item.video?.user;
          itemUserId = item.video?.userId;
          itemUsername = item.video?.username;
        }

        if (itemUser?.id) ownershipStats.uniqueUserIds.add(itemUser.id);
        if (itemUser?.username) ownershipStats.uniqueUsernames.add(itemUser.username);
        if (itemUsername) ownershipStats.uniqueUsernames.add(itemUsername);

        currentPageItems++;

        // Log first few items from first page for manual verification
        if (index < 3) {
          console.log(`   Item ${index + 1} (${item.type}): user=${itemUser?.username} (ID: ${itemUser?.id})`);
        }

        // Track page breakdown
        if (currentPageItems === itemsPerPage || index === allItems.length - 1) {
          const currentPage = Math.floor(index / itemsPerPage) + 1;
          ownershipStats.pageBreakdown.push({
            page: currentPage,
            items: currentPageItems,
            images: currentPageImages,
            videos: currentPageVideos
          });
          currentPageItems = 0;
          currentPageImages = 0;
          currentPageVideos = 0;
        }
      });

      console.log(`   📊 TOTAL content breakdown: ${ownershipStats.imageItems} images, ${ownershipStats.videoItems} videos`);
      console.log(`   👥 Unique user IDs found across ALL pages: ${ownershipStats.uniqueUserIds.size}`);
      console.log(`   👥 Unique usernames found across ALL pages: ${ownershipStats.uniqueUsernames.size}`);

      // Show page-by-page breakdown
      console.log(`\n   📄 Page-by-page breakdown:`);
      ownershipStats.pageBreakdown.forEach(pageInfo => {
        console.log(`      Page ${pageInfo.page}: ${pageInfo.items} items (${pageInfo.images} images, ${pageInfo.videos} videos)`);
      });

      // Critical assertion: All content across ALL pages should belong to exactly one user
      expect(ownershipStats.uniqueUserIds.size).toBeLessThanOrEqual(1);
      expect(ownershipStats.uniqueUsernames.size).toBeLessThanOrEqual(1);

      // If we have content, verify it belongs to the expected user
      if (ownershipStats.uniqueUserIds.size === 1) {
        const actualUserId = Array.from(ownershipStats.uniqueUserIds)[0];
        if (targetUserId) {
          expect(actualUserId).toBe(targetUserId);
          console.log(`   ✅ ALL content across ${pageCount} pages belongs to expected user ID: ${actualUserId}`);
        } else {
          console.log(`   ✅ ALL content across ${pageCount} pages belongs to single user ID: ${actualUserId}`);
        }
      }

      if (ownershipStats.uniqueUsernames.size === 1) {
        const actualUsername = Array.from(ownershipStats.uniqueUsernames)[0];
        expect(actualUsername).toBe(testUserIdentifier);
        console.log(`   ✅ ALL content across ${pageCount} pages belongs to expected username: ${actualUsername}`);
      }

      console.log(`🎉 COMPREHENSIVE User ownership validation PASSED - all ${allItems.length} items across ${pageCount} pages belong to the correct user!`);
    }, 60000); // 60 second timeout for comprehensive pagination test

    it('should validate ownership across different privacy levels (ALL pages)', async () => {
      console.log('\n🔍 Testing Ownership Across Privacy Levels (ALL pages)');
      console.log('======================================================');

      const privacyLevels = ['public', 'private', 'all'];
      const allUserIds = new Set<string>();
      const allUsernames = new Set<string>();
      const privacyStats: Record<string, {pages: number, totalItems: number}> = {};

      for (const privacy of privacyLevels) {
        console.log(`\n📋 Testing privacy=${privacy} across ALL pages...`);

        // Collect all items for this privacy level using pagination
        let privacyItems: any[] = [];
        let currentCursor: string | undefined = undefined;
        let pageCount = 0;
        let hasMore = true;
        const maxPages = 5; // Reasonable limit for privacy level testing in E2E

        while (hasMore && pageCount < maxPages) {
          pageCount++;

          const pageResponse = await request(baseHost)
            .get(`/feed/user/${testUserIdentifier}`)
            .query({
              privacy,
              limit: 15, // Smaller pages for more thorough testing
              ...(currentCursor ? { cursor: currentCursor } : {})
            })
            .set('Authorization', `Bearer ${userToken}`)
            .expect(200);

          const pageItems = pageResponse.body.items || [];

          if (pageItems.length === 0) {
            break;
          }

          privacyItems = privacyItems.concat(pageItems);

          // Check pagination
          hasMore = pageResponse.body.pagination?.hasMore === true;
          currentCursor = pageResponse.body.pagination?.nextCursor;

          if (!hasMore || !currentCursor) {
            break;
          }

          // Small delay between requests
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        privacyStats[privacy] = { pages: pageCount, totalItems: privacyItems.length };
        console.log(`   📊 Privacy=${privacy}: ${privacyItems.length} items across ${pageCount} pages`);

        if (privacyItems.length > 0) {
          // Create mock response for validation
          const privacyResponse = { items: privacyItems };

          // Validate ownership for this privacy level
          validateUserOwnership(privacyResponse, testUserIdentifier, targetUserId);

          // Collect user information across all pages
          privacyItems.forEach((item: any) => {
            let itemUser = null;
            if (item.type === 'image') {
              itemUser = item.imageCompletion?.user;
            } else if (item.type === 'video') {
              itemUser = item.video?.user;
            }

            if (itemUser?.id) allUserIds.add(itemUser.id);
            if (itemUser?.username) allUsernames.add(itemUser.username);
          });

          console.log(`   ✅ Ownership validated for privacy=${privacy} across ${pageCount} pages`);
        } else {
          console.log(`   ⚠️  No content for privacy=${privacy}`);
        }
      }

      // Final cross-privacy validation with detailed statistics
      console.log(`\n📊 COMPREHENSIVE Cross-privacy analysis:`);
      console.log(`   👥 Total unique user IDs across ALL privacy levels and pages: ${allUserIds.size}`);
      console.log(`   👥 Total unique usernames across ALL privacy levels and pages: ${allUsernames.size}`);

      console.log(`\n   📄 Privacy level breakdown:`);
      Object.entries(privacyStats).forEach(([privacy, stats]) => {
        console.log(`      ${privacy}: ${stats.totalItems} items across ${stats.pages} pages`);
      });

      const totalItemsAcrossPrivacy = Object.values(privacyStats).reduce((sum, stats) => sum + stats.totalItems, 0);
      const totalPagesAcrossPrivacy = Object.values(privacyStats).reduce((sum, stats) => sum + stats.pages, 0);

      console.log(`   📊 GRAND TOTAL: ${totalItemsAcrossPrivacy} items across ${totalPagesAcrossPrivacy} pages across all privacy levels`);

      // All content across all privacy levels and pages should belong to the same user
      expect(allUserIds.size).toBeLessThanOrEqual(1);
      expect(allUsernames.size).toBeLessThanOrEqual(1);

      console.log(`🎉 COMPREHENSIVE Cross-privacy ownership validation PASSED across ${totalItemsAcrossPrivacy} items!`);
    }, 90000); // 90 second timeout for cross-privacy comprehensive test

    it('should validate ownership when viewing another user profile (if accessible)', async () => {
      console.log('\n🔍 Testing Ownership for Different User Profile');
      console.log('==============================================');

      // Test with a different user identifier (this might fail if user doesn't exist)
      const otherUserIdentifier = 'testuser2'; // This should be configurable
      console.log(`📋 Testing with other user: ${otherUserIdentifier}`);

      const response = await request(baseHost)
        .get(`/feed/user/${otherUserIdentifier}`)
        .query({ privacy: 'public', limit: 20 }) // Only public content for other users
        .set('Authorization', `Bearer ${userToken}`);

      if (response.status === 200 && response.body.items.length > 0) {
        console.log(`📊 Items returned for ${otherUserIdentifier}: ${response.body.items.length}`);

        // Validate that all items belong to the requested user (not the authenticated user)
        const otherUserStats = {
          uniqueUserIds: new Set<string>(),
          uniqueUsernames: new Set<string>(),
        };

        response.body.items.forEach((item: any) => {
          let itemUser = null;
          if (item.type === 'image') {
            itemUser = item.imageCompletion?.user;
          } else if (item.type === 'video') {
            itemUser = item.video?.user;
          }

          if (itemUser?.id) otherUserStats.uniqueUserIds.add(itemUser.id);
          if (itemUser?.username) otherUserStats.uniqueUsernames.add(itemUser.username);
        });

        console.log(`   👥 Unique user IDs: ${otherUserStats.uniqueUserIds.size}`);
        console.log(`   👥 Unique usernames: ${otherUserStats.uniqueUsernames.size}`);

        // All content should belong to exactly one user (the requested user)
        expect(otherUserStats.uniqueUserIds.size).toBeLessThanOrEqual(1);
        expect(otherUserStats.uniqueUsernames.size).toBeLessThanOrEqual(1);

        // Content should NOT belong to the authenticated user
        if (otherUserStats.uniqueUsernames.size === 1) {
          const actualUsername = Array.from(otherUserStats.uniqueUsernames)[0];
          expect(actualUsername).not.toBe(testUserIdentifier);
          console.log(`   ✅ Content belongs to ${actualUsername}, not ${testUserIdentifier}`);
        }

        if (targetUserId && otherUserStats.uniqueUserIds.size === 1) {
          const actualUserId = Array.from(otherUserStats.uniqueUserIds)[0];
          expect(actualUserId).not.toBe(targetUserId);
          console.log(`   ✅ Content belongs to different user ID: ${actualUserId}`);
        }

        console.log('🎉 Cross-user ownership validation PASSED!');
      } else if (response.status === 404) {
        console.log(`   ⚠️  User ${otherUserIdentifier} not found - skipping cross-user test`);
      } else if (response.status === 403) {
        console.log(`   ⚠️  Access forbidden to ${otherUserIdentifier} - this is expected behavior`);
      } else {
        console.log(`   ⚠️  No content for ${otherUserIdentifier} or unexpected status: ${response.status}`);
      }
    });
  });

  describe('Model ID Filtering', () => {
    let baselineResponse: any;
    let availableModelIds: string[] = [];
    let testTimestamp: string;

    beforeAll(async () => {
      testTimestamp = new Date().toISOString();
      console.log(`\n🧪 Starting Model ID Filtering tests at ${testTimestamp}`);
      console.log(`📍 Testing against: ${baseHost}`);
      console.log(`👤 Test user: ${testUserIdentifier}`);
    });

    describe('1. Baseline (no filtering)', () => {
      it('should return all user content when no modelIds parameter is provided', async () => {
        console.log('\n📋 Test 1: Baseline - No filtering');
        const requestUrl = `/feed/user/${testUserIdentifier}`;
        console.log(`🔗 Request: GET ${requestUrl}`);

        const response = await request(baseHost)
          .get(requestUrl)
          .query({ limit: 50 }) // Get more items to analyze
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Store baseline for comparison
        baselineResponse = response.body;

        // Extract model IDs from the response for use in subsequent tests
        if (response.body.items && response.body.items.length > 0) {
          response.body.items.forEach((item: any) => {
            let modelId = null;
            if (item.type === 'image' && item.imageCompletion?.modelId) {
              modelId = item.imageCompletion.modelId;
            } else if (item.type === 'video' && item.video?.modelId) {
              modelId = item.video.modelId;
            }

            if (modelId && !availableModelIds.includes(modelId)) {
              availableModelIds.push(modelId);
            }
          });

          console.log(`🎯 Available model IDs found: ${availableModelIds.length}`);
          console.log(`📝 Model IDs: ${availableModelIds.slice(0, 5).join(', ')}${availableModelIds.length > 5 ? '...' : ''}`);
        } else {
          console.log('⚠️  No items found in baseline response - this may affect subsequent tests');
        }

        // Baseline should have content (assuming test user has content)
        expect(response.body.items).toBeDefined();
        console.log('✅ Baseline test completed successfully');
      }, 30000);
    });

    describe('2. Empty filter array', () => {
      it('should behave same as no parameter when modelIds[] is empty', async () => {
        console.log('\n📋 Test 2: Empty filter array');
        const requestUrl = `/feed/user/${testUserIdentifier}?modelIds[]=`;
        console.log(`🔗 Request: GET ${requestUrl}`);

        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ 'modelIds[]': '', limit: 50 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);
        console.log(`📊 Baseline items: ${baselineResponse?.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Should return same amount as baseline (or similar, accounting for potential new content)
        const itemCountDiff = Math.abs((response.body.items?.length || 0) - (baselineResponse?.items?.length || 0));
        console.log(`📈 Item count difference from baseline: ${itemCountDiff}`);

        // Allow small differences due to potential new content, but should be roughly the same
        expect(itemCountDiff).toBeLessThanOrEqual(5);
        console.log('✅ Empty filter array test completed');
      }, 30000);
    });

    describe('3. Single valid model ID', () => {
      it('should return only content created with the specified model', async () => {
        console.log('\n📋 Test 3: Single valid model ID');

        // Properly skip test if no model IDs available
        if (availableModelIds.length === 0) {
          throw new Error('No model IDs available from baseline - cannot test model filtering. This indicates that the user content does not have model ID information.');
        }

        const testModelId = availableModelIds[0];
        const requestUrl = `/feed/user/${testUserIdentifier}?modelIds[]=${testModelId}`;
        console.log(`🔗 Request: GET ${requestUrl}`);
        console.log(`🎯 Testing with model ID: ${testModelId}`);

        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ 'modelIds[]': testModelId, limit: 50 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Model ID filtering should return content with the specified model ID
        // If no items are returned, this indicates a bug in the filtering logic
        expect(response.body.items).toBeDefined();
        expect(response.body.items.length).toBeGreaterThan(0);

        // Verify all returned items have the correct model ID
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          expect(itemModelId).toBe(testModelId);
        });

        console.log('✅ All returned items have correct model ID');
      }, 30000);
    });

    describe('4. Multiple valid model IDs', () => {
      it('should return content created with any of the specified models (OR logic)', async () => {
        console.log('\n📋 Test 4: Multiple valid model IDs');

        // Properly skip test if insufficient model IDs available
        if (availableModelIds.length < 2) {
          throw new Error('Need at least 2 model IDs for multiple model ID filtering test. This indicates insufficient model ID diversity in user content.');
        }

        const testModelIds = availableModelIds.slice(0, 2);
        const queryString = testModelIds.map(id => `modelIds[]=${id}`).join('&');
        const requestUrl = `/feed/user/${testUserIdentifier}?${queryString}`;
        console.log(`🔗 Request: GET ${requestUrl}`);
        console.log(`🎯 Testing with model IDs: ${testModelIds.join(', ')}`);

        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ 'modelIds[]': testModelIds, limit: 50 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Multiple model ID filtering should return content with any of the specified model IDs
        // If no items are returned, this indicates a bug in the filtering logic
        expect(response.body.items).toBeDefined();
        expect(response.body.items.length).toBeGreaterThan(0);

        // Verify all returned items have one of the specified model IDs
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          expect(testModelIds).toContain(itemModelId);
        });

        console.log('✅ All returned items have correct model IDs');
      }, 30000);
    });

    describe('5. Invalid/non-existent model IDs', () => {
      it('should return empty array or appropriate error for invalid model IDs', async () => {
        console.log('\n📋 Test 5: Invalid/non-existent model IDs');

        const invalidModelId = 'invalid-model-id-12345';
        const requestUrl = `/feed/user/${testUserIdentifier}?modelIds[]=${invalidModelId}`;
        console.log(`🔗 Request: GET ${requestUrl}`);
        console.log(`🎯 Testing with invalid model ID: ${invalidModelId}`);

        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ 'modelIds[]': invalidModelId, limit: 50 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200); // Should still return 200, just with empty results

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Should return empty array for invalid model ID
        expect(response.body.items).toEqual([]);
        console.log('✅ Invalid model ID correctly returns empty array');
      }, 30000);
    });

    describe('6. Mixed valid and invalid model IDs', () => {
      it('should return content for valid model IDs only, ignoring invalid ones', async () => {
        console.log('\n📋 Test 6: Mixed valid and invalid model IDs');

        // Properly skip test if no valid model IDs available
        if (availableModelIds.length === 0) {
          throw new Error('No valid model IDs available for mixed valid/invalid filtering test. This indicates that the user content does not have model ID information.');
        }

        const validModelId = availableModelIds[0];
        const invalidModelId = 'invalid-model-id-67890';
        const testModelIds = [validModelId, invalidModelId];

        const queryString = testModelIds.map(id => `modelIds[]=${id}`).join('&');
        const requestUrl = `/feed/user/${testUserIdentifier}?${queryString}`;
        console.log(`🔗 Request: GET ${requestUrl}`);
        console.log(`🎯 Testing with valid ID: ${validModelId}`);
        console.log(`🎯 Testing with invalid ID: ${invalidModelId}`);

        const response = await request(baseHost)
          .get(`/feed/user/${testUserIdentifier}`)
          .query({ 'modelIds[]': testModelIds, limit: 50 })
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        console.log(`📊 Response status: ${response.status}`);
        console.log(`📦 Items returned: ${response.body.items?.length || 0}`);

        validateFeedResponse(response.body);

        // Mixed valid/invalid model ID filtering should return content for valid IDs only
        // Invalid IDs should be ignored, not cause the entire filter to fail
        expect(response.body.items).toBeDefined();
        expect(response.body.items.length).toBeGreaterThan(0);

        // Verify all returned items have the valid model ID (should ignore invalid one)
        response.body.items.forEach((item: any, index: number) => {
          let itemModelId = null;
          if (item.type === 'image' && item.imageCompletion?.modelId) {
            itemModelId = item.imageCompletion.modelId;
          } else if (item.type === 'video' && item.video?.modelId) {
            itemModelId = item.video.modelId;
          }

          console.log(`📄 Item ${index + 1}: type=${item.type}, modelId=${itemModelId}`);
          expect(itemModelId).toBe(validModelId);
        });

        console.log('✅ All returned items have the valid model ID (invalid ID ignored)');
      }, 30000);
    });

    afterAll(() => {
      console.log(`\n🏁 Model ID Filtering tests completed at ${new Date().toISOString()}`);
      console.log(`📊 Test Summary:`);
      console.log(`   - Baseline test: Validates basic feed functionality without filtering`);
      console.log(`   - Empty array test: Validates empty filter array behavior`);
      console.log(`   - Single model ID test: Validates filtering by single model ID`);
      console.log(`   - Multiple model IDs test: Validates filtering by multiple model IDs (OR logic)`);
      console.log(`   - Invalid model ID test: Validates handling of invalid model IDs`);
      console.log(`   - Mixed valid/invalid test: Validates filtering with mixed valid/invalid IDs`);
      console.log(`\n📋 Note: Tests will fail if model ID filtering is not working correctly`);
      console.log(`💡 To monitor API logs during testing:`);
      console.log(`   docker compose logs private-api | grep -E "(modelIds|feed/user)"`);
    });
  });
});
