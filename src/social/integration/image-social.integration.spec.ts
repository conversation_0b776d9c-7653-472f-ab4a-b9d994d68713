import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EntityValidatorService } from '../service/entity-validator.service';
import { ImageCompletionEntityValidator } from '../service/image-completion-entity.validator';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import {
  ImageCompletionEntity,
  StatusEnum,
  PrivacyEnum,
} from '../../image-completion/entity/image-completion.entity';
import { ImageCompletionManager } from '../../image-completion/service/manager';
import { ImageCompletionInteractionListener } from '../../image-completion/listener/image-completion-interaction.listener';
import { Logger } from 'nestjs-pino';

describe('Image Social Integration', () => {
  let entityValidatorService: EntityValidatorService;
  let imageValidator: ImageCompletionEntityValidator;
  let imageCompletionProvider: jest.Mocked<ImageCompletionProvider>;

  const createMockImage = (
    overrides: Partial<ImageCompletionEntity> = {},
  ): any => ({
    id: 'image-123',
    userId: 'user-456',
    status: StatusEnum.READY,
    privacy: PrivacyEnum.PUBLIC,
    createdAt: new Date(),
    deletedAt: null,
    prompt: 'Beautiful landscape painting',
    likes: 15,
    comments: 8,
    isNsfw: false,
    isUnsafe: false,
    hidePrompt: false,
    imagePaths: {
      thumbnail: 'https://cdn.example.com/thumb.jpg',
      original: 'https://cdn.example.com/original.jpg',
    },
    models: [],
    hasModel: jest.fn().mockReturnValue(false),
    ...overrides,
  });

  const mockImage = createMockImage();

  beforeEach(async () => {
    const mockImageCompletionProvider = {
      findOne: jest.fn(),
    };

    const mockLogger = {
      debug: jest.fn(),
      error: jest.fn(),
      log: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityValidatorService,
        ImageCompletionEntityValidator,
        {
          provide: ImageCompletionProvider,
          useValue: mockImageCompletionProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    entityValidatorService = module.get<EntityValidatorService>(
      EntityValidatorService,
    );
    imageValidator = module.get<ImageCompletionEntityValidator>(
      ImageCompletionEntityValidator,
    );
    imageCompletionProvider = module.get(ImageCompletionProvider);

    // Register the image validator
    entityValidatorService.registerValidator('image', imageValidator);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Image Entity Validation', () => {
    it('should register image validator successfully', () => {
      // Assert
      expect(entityValidatorService.hasValidator('image')).toBe(true);
      expect(entityValidatorService.getRegisteredEntityTypes()).toContain(
        'image',
      );
    });

    it('should validate image entity exists', async () => {
      // Arrange
      imageCompletionProvider.findOne.mockResolvedValue(mockImage);

      // Act & Assert
      await expect(
        entityValidatorService.validateEntityExists('image', 'image-123'),
      ).resolves.not.toThrow();
    });

    it('should throw error for non-existent image', async () => {
      // Arrange
      imageCompletionProvider.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        entityValidatorService.validateEntityExists('image', 'nonexistent'),
      ).rejects.toThrow(
        'image with ID nonexistent not found or cannot be accessed',
      );
    });
  });

  describe('Image Entity Info', () => {
    it('should get detailed entity info for notifications', async () => {
      // Arrange
      imageCompletionProvider.findOne.mockResolvedValue(mockImage);

      // Act
      const result = await imageValidator.getEntityInfo('image-123');

      // Assert
      expect(result).toEqual({
        id: 'image-123',
        ownerId: 'user-456',
        title: 'Beautiful landscape painting',
        thumbnail: 'https://cdn.example.com/thumb.jpg',
        status: StatusEnum.READY,
        privacy: PrivacyEnum.PUBLIC,
        createdAt: mockImage.createdAt,
        likes: 15,
        comments: 8,
        isNsfw: false,
        hidePrompt: false,
        prompt: 'Beautiful landscape painting',
        imagePaths: mockImage.imagePaths,
        hasWatermark: undefined, // Not set in mock
        generationSettings: undefined, // Not set in mock
      });
    });

    it('should handle hidden prompt images', async () => {
      // Arrange
      const hiddenPromptImage = createMockImage({ hidePrompt: true });
      imageCompletionProvider.findOne.mockResolvedValue(hiddenPromptImage);

      // Act
      const result = await imageValidator.getEntityInfo('image-123');

      // Assert
      expect(result.title).toBe('Generated Image');
      expect(result.prompt).toBeNull();
      expect(result.hidePrompt).toBe(true);
    });
  });

  describe('API Naming Convention', () => {
    it('should use "image" as entity type (not "imageCompletion")', () => {
      // This test verifies that we're using the simplified "image" naming
      // in external APIs rather than the internal "imageCompletion" naming

      // Assert
      expect(entityValidatorService.hasValidator('image')).toBe(true);
      expect(entityValidatorService.hasValidator('imageCompletion')).toBe(
        false,
      );

      // The validator should be registered under "image" for external API use
      const validator = entityValidatorService.getValidator('image');
      expect(validator).toBe(imageValidator);
    });
  });

  describe('Error Handling', () => {
    it('should handle private images correctly', async () => {
      // Arrange
      const privateImage = createMockImage({ privacy: PrivacyEnum.PRIVATE });
      imageCompletionProvider.findOne.mockResolvedValue(privateImage);

      // Act & Assert
      await expect(
        entityValidatorService.validateEntityExists('image', 'image-123'),
      ).rejects.toThrow(
        'image with ID image-123 not found or cannot be accessed',
      );
    });

    it('should handle generating images correctly', async () => {
      // Arrange
      const generatingImage = createMockImage({
        status: StatusEnum.GENERATING,
      });
      imageCompletionProvider.findOne.mockResolvedValue(generatingImage);

      // Act & Assert
      await expect(
        entityValidatorService.validateEntityExists('image', 'image-123'),
      ).rejects.toThrow(
        'image with ID image-123 not found or cannot be accessed',
      );
    });

    it('should handle unsafe images correctly', async () => {
      // Arrange
      const unsafeImage = createMockImage({ isUnsafe: true });
      imageCompletionProvider.findOne.mockResolvedValue(unsafeImage);

      // Act & Assert
      await expect(
        entityValidatorService.validateEntityExists('image', 'image-123'),
      ).rejects.toThrow(
        'image with ID image-123 not found or cannot be accessed',
      );
    });
  });

  describe('Image Completion Interaction Listener Integration', () => {
    let eventEmitter: EventEmitter2;
    let imageCompletionManager: jest.Mocked<ImageCompletionManager>;
    let interactionListener: ImageCompletionInteractionListener;

    beforeEach(async () => {
      const mockImageCompletionManager = {
        incrementLikeCount: jest.fn(),
        decrementLikeCount: jest.fn(),
        incrementCommentCount: jest.fn(),
        decrementCommentCount: jest.fn(),
      };

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EventEmitter2,
          ImageCompletionInteractionListener,
          {
            provide: ImageCompletionManager,
            useValue: mockImageCompletionManager,
          },
          {
            provide: Logger,
            useValue: {
              debug: jest.fn(),
              log: jest.fn(),
              error: jest.fn(),
            },
          },
        ],
      }).compile();

      eventEmitter = module.get<EventEmitter2>(EventEmitter2);
      imageCompletionManager = module.get(ImageCompletionManager);
      interactionListener = module.get<ImageCompletionInteractionListener>(
        ImageCompletionInteractionListener,
      );
    });

    it('should handle entity.liked events for images', async () => {
      // Arrange
      imageCompletionManager.incrementLikeCount.mockResolvedValue();

      // Act - Call the listener method directly since we're testing in isolation
      await interactionListener.handleEntityLiked({
        likeId: 'like-123',
        entityType: 'image',
        entityId: 'image-456',
        userId: 'user-789',
      });

      // Assert
      expect(imageCompletionManager.incrementLikeCount).toHaveBeenCalledWith(
        'image-456',
      );
    });

    it('should handle entity.comment.created events for images', async () => {
      // Arrange
      imageCompletionManager.incrementCommentCount.mockResolvedValue();

      // Act - Call the listener method directly since we're testing in isolation
      await interactionListener.handleCommentCreated({
        commentId: 'comment-123',
        entityType: 'image',
        entityId: 'image-456',
        userId: 'user-789',
        comment: 'Great image!',
      });

      // Assert
      expect(imageCompletionManager.incrementCommentCount).toHaveBeenCalledWith(
        'image-456',
      );
    });

    it('should ignore events for non-image entity types', async () => {
      // Act - Call the listener method directly
      await interactionListener.handleEntityLiked({
        likeId: 'like-123',
        entityType: 'video',
        entityId: 'video-456',
        userId: 'user-789',
      });

      // Assert
      expect(imageCompletionManager.incrementLikeCount).not.toHaveBeenCalled();
    });
  });
});
