/**
 * Base class for entity like events
 */
export abstract class BaseEntityLikeEvent {
  constructor(
    public readonly likeId: string,
    public readonly entityType: string,
    public readonly entityId: string,
    public readonly userId: string,
    public readonly timestamp: Date = new Date(),
  ) {}
}

/**
 * Event emitted when an entity is liked
 */
export class EntityLikedEvent extends BaseEntityLikeEvent {
  constructor(
    likeId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly entityOwnerId?: string,
    timestamp?: Date,
  ) {
    super(likeId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.liked';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.liked`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      likeId: this.likeId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      entityOwnerId: this.entityOwnerId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'entity.liked',
    };
  }
}

/**
 * Event emitted when an entity is unliked
 */
export class EntityUnlikedEvent extends BaseEntityLikeEvent {
  constructor(
    likeId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly entityOwnerId?: string,
    timestamp?: Date,
  ) {
    super(likeId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.unliked';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.unliked`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      likeId: this.likeId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      entityOwnerId: this.entityOwnerId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'entity.unliked',
    };
  }
}

/**
 * Event emitted when an entity reaches a like milestone
 */
export class EntityLikeMilestoneEvent extends BaseEntityLikeEvent {
  constructor(
    likeId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly milestone: number,
    public readonly totalLikes: number,
    public readonly entityOwnerId?: string,
    timestamp?: Date,
  ) {
    super(likeId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.like.milestone';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.like.milestone`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      likeId: this.likeId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      milestone: this.milestone,
      totalLikes: this.totalLikes,
      entityOwnerId: this.entityOwnerId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'entity.like.milestone',
    };
  }

  /**
   * Check if a like count represents a milestone
   */
  static isMilestone(likeCount: number): boolean {
    // Define milestones: 1, 5, 10, 25, 50, 100, 250, 500, 1000, etc.
    const milestones = [
      1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000,
    ];
    return milestones.includes(likeCount);
  }

  /**
   * Get the milestone value for a given like count
   */
  static getMilestone(likeCount: number): number | null {
    if (this.isMilestone(likeCount)) {
      return likeCount;
    }
    return null;
  }
}
