/**
 * Base class for entity comment events
 */
export abstract class BaseEntityCommentEvent {
  constructor(
    public readonly commentId: string,
    public readonly entityType: string,
    public readonly entityId: string,
    public readonly userId: string,
    public readonly timestamp: Date = new Date(),
  ) {}
}

/**
 * Event emitted when a comment is created on an entity
 */
export class EntityCommentCreatedEvent extends BaseEntityCommentEvent {
  constructor(
    commentId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly comment: string,
    timestamp?: Date,
  ) {
    super(commentId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.comment.created';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.comment.created`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      commentId: this.commentId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      comment: this.comment,
      timestamp: this.timestamp.toISOString(),
      eventType: 'comment.created',
    };
  }
}

/**
 * Event emitted when a comment is deleted from an entity
 */
export class EntityCommentDeletedEvent extends BaseEntityCommentEvent {
  constructor(
    commentId: string,
    entityType: string,
    entityId: string,
    userId: string,
    timestamp?: Date,
  ) {
    super(commentId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.comment.deleted';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.comment.deleted`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      commentId: this.commentId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'comment.deleted',
    };
  }
}

/**
 * Event emitted when a comment is liked
 */
export class EntityCommentLikedEvent extends BaseEntityCommentEvent {
  constructor(
    commentId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly commentLikeId: string,
    public readonly commentOwnerId: string,
    timestamp?: Date,
  ) {
    super(commentId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.comment.liked';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.comment.liked`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      commentId: this.commentId,
      commentLikeId: this.commentLikeId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      commentOwnerId: this.commentOwnerId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'comment.liked',
    };
  }
}

/**
 * Event emitted when a comment is unliked
 */
export class EntityCommentUnlikedEvent extends BaseEntityCommentEvent {
  constructor(
    commentId: string,
    entityType: string,
    entityId: string,
    userId: string,
    public readonly commentLikeId: string,
    public readonly commentOwnerId: string,
    timestamp?: Date,
  ) {
    super(commentId, entityType, entityId, userId, timestamp);
  }

  /**
   * Get event name for the event emitter
   */
  static getEventName(): string {
    return 'entity.comment.unliked';
  }

  /**
   * Get entity-specific event name
   */
  getEntitySpecificEventName(): string {
    return `${this.entityType}.comment.unliked`;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      commentId: this.commentId,
      commentLikeId: this.commentLikeId,
      entityType: this.entityType,
      entityId: this.entityId,
      userId: this.userId,
      commentOwnerId: this.commentOwnerId,
      timestamp: this.timestamp.toISOString(),
      eventType: 'comment.unliked',
    };
  }
}
