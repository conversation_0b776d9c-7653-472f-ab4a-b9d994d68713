/**
 * Interface for entity-specific validation and operations
 */
export interface EntityValidator {
  /**
   * Validate that an entity exists and can be commented on/liked
   */
  validateExists(entityId: string): Promise<void>;

  /**
   * Get entity information for notifications
   */
  getEntityInfo(entityId: string): Promise<{
    id: string;
    ownerId: string;
    title?: string;
    thumbnail?: string;
    [key: string]: any;
  }>;
}
