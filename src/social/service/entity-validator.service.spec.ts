import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { EntityValidatorService } from './entity-validator.service';
import { EntityValidator } from './entity-validator.interface';
import { Logger } from 'nestjs-pino';

describe('EntityValidatorService', () => {
  let service: EntityValidatorService;
  let logger: jest.Mocked<Logger>;
  let mockValidator: jest.Mocked<EntityValidator>;

  beforeEach(async () => {
    const mockLogger = {
      warn: jest.fn(),
      log: jest.fn(),
      error: jest.fn(),
    };

    mockValidator = {
      validateExists: jest.fn(),
      getEntityInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityValidatorService,
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<EntityValidatorService>(EntityValidatorService);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('registerValidator', () => {
    it('should register a validator for an entity type', () => {
      // Act
      service.registerValidator('video', mockValidator);

      // Assert
      expect(service.hasValidator('video')).toBe(true);
      expect(service.getRegisteredEntityTypes()).toContain('video');
      expect(logger.log).toHaveBeenCalledWith('Registered entity validator', {
        entityType: 'video',
      });
    });

    it('should normalize entity type to lowercase', () => {
      // Act
      service.registerValidator('VIDEO', mockValidator);

      // Assert
      expect(service.hasValidator('video')).toBe(true);
      expect(service.hasValidator('VIDEO')).toBe(true);
    });

    it('should warn when overriding existing validator', () => {
      // Arrange
      service.registerValidator('video', mockValidator);

      // Act
      service.registerValidator('video', mockValidator);

      // Assert
      expect(logger.warn).toHaveBeenCalledWith(
        'Overriding existing validator for entity type',
        { entityType: 'video' },
      );
    });

    it('should handle entity types with special characters', () => {
      // Act
      service.registerValidator('image-completion', mockValidator);

      // Assert
      expect(service.hasValidator('image-completion')).toBe(true);
      expect(service.hasValidator('IMAGE-COMPLETION')).toBe(true);
    });
  });

  describe('getValidator', () => {
    beforeEach(() => {
      service.registerValidator('video', mockValidator);
    });

    it('should return registered validator', () => {
      // Act
      const validator = service.getValidator('video');

      // Assert
      expect(validator).toBe(mockValidator);
    });

    it('should handle case-insensitive entity types', () => {
      // Act
      const validator = service.getValidator('VIDEO');

      // Assert
      expect(validator).toBe(mockValidator);
    });

    it('should throw BadRequestException for unregistered entity type', () => {
      // Act & Assert
      expect(() => service.getValidator('unknown')).toThrow(
        BadRequestException,
      );
      expect(() => service.getValidator('unknown')).toThrow(
        'No validator registered for entity type: unknown',
      );
    });
  });

  describe('hasValidator', () => {
    beforeEach(() => {
      service.registerValidator('video', mockValidator);
    });

    it('should return true for registered entity type', () => {
      // Act & Assert
      expect(service.hasValidator('video')).toBe(true);
    });

    it('should return false for unregistered entity type', () => {
      // Act & Assert
      expect(service.hasValidator('unknown')).toBe(false);
    });

    it('should handle case-insensitive checks', () => {
      // Act & Assert
      expect(service.hasValidator('VIDEO')).toBe(true);
      expect(service.hasValidator('Video')).toBe(true);
    });
  });

  describe('getRegisteredEntityTypes', () => {
    it('should return empty array when no validators registered', () => {
      // Act
      const types = service.getRegisteredEntityTypes();

      // Assert
      expect(types).toEqual([]);
    });

    it('should return all registered entity types', () => {
      // Arrange
      const mockImageValidator = { ...mockValidator };
      service.registerValidator('video', mockValidator);
      service.registerValidator('image', mockImageValidator);

      // Act
      const types = service.getRegisteredEntityTypes();

      // Assert
      expect(types).toContain('video');
      expect(types).toContain('image');
      expect(types).toHaveLength(2);
    });
  });

  describe('validateEntityExists', () => {
    beforeEach(() => {
      service.registerValidator('video', mockValidator);
    });

    it('should validate entity exists successfully', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      mockValidator.validateExists.mockResolvedValue();

      // Act
      await service.validateEntityExists(entityType, entityId);

      // Assert
      expect(mockValidator.validateExists).toHaveBeenCalledWith(entityId);
    });

    it('should throw NotFoundException when validator throws error', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const originalError = new Error('Video not found');
      mockValidator.validateExists.mockRejectedValue(originalError);

      // Act & Assert
      await expect(
        service.validateEntityExists(entityType, entityId),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.validateEntityExists(entityType, entityId),
      ).rejects.toThrow(
        'video with ID video-123 not found or cannot be accessed',
      );

      expect(logger.error).toHaveBeenCalledWith('Entity validation failed', {
        entityType,
        entityId,
        error: originalError.message,
      });
    });

    it('should throw BadRequestException for unregistered entity type', async () => {
      // Act & Assert
      await expect(
        service.validateEntityExists('unknown', 'id-123'),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('createEntityValidator', () => {
    beforeEach(() => {
      service.registerValidator('video', mockValidator);
    });

    it('should create entity validator wrapper', () => {
      // Act
      const wrapper = service.createEntityValidator('video');

      // Assert
      expect(wrapper).toBeDefined();
      expect(typeof wrapper.validateExists).toBe('function');
      expect(typeof wrapper.getEntityInfo).toBe('function');
    });

    it('should delegate validateExists to base validator', async () => {
      // Arrange
      const wrapper = service.createEntityValidator('video');
      const entityId = 'video-123';
      mockValidator.validateExists.mockResolvedValue();

      // Act
      await wrapper.validateExists(entityId);

      // Assert
      expect(mockValidator.validateExists).toHaveBeenCalledWith(entityId);
    });

    it('should delegate getEntityInfo to base validator', async () => {
      // Arrange
      const wrapper = service.createEntityValidator('video');
      const entityId = 'video-123';
      const mockInfo = { id: entityId, ownerId: 'user-1' };
      mockValidator.getEntityInfo.mockResolvedValue(mockInfo);

      // Act
      const result = await wrapper.getEntityInfo(entityId);

      // Assert
      expect(result).toBe(mockInfo);
      expect(mockValidator.getEntityInfo).toHaveBeenCalledWith(entityId);
    });
  });

  describe('normalizeEntityType', () => {
    it('should normalize various entity type formats', () => {
      // Test through public methods that use normalization
      service.registerValidator('Video-Type', mockValidator);

      expect(service.hasValidator('video-type')).toBe(true);
      expect(service.hasValidator('VIDEO-TYPE')).toBe(true);
      expect(service.hasValidator('Video-Type')).toBe(true);
    });
  });
});
