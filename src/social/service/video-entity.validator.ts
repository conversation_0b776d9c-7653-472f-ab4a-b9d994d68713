import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { StatusEnum, PrivacyEnum } from '../../video/entity/video.entity';
import { VideoProvider } from '../../video/service/provider';
import { EntityValidator } from './entity-validator.interface';

@Injectable()
export class VideoEntityValidator implements EntityValidator {
  constructor(
    private readonly videoProvider: VideoProvider,
    private readonly logger: Logger,
  ) {}

  /**
   * Validate that a video exists and can be commented on/liked
   */
  async validateExists(entityId: string): Promise<void> {
    try {
      this.logger.debug('VideoEntityValidator: Validating video existence', {
        entityId,
      });

      // Use VideoProvider for data access - minimal relations for performance
      const video = await this.videoProvider.findOne(entityId);

      if (!video) {
        throw new NotFoundException('Video not found');
      }

      // Check if video is soft deleted
      if (video.deletedAt) {
        throw new NotFoundException('Video has been deleted');
      }

      // Check if video is ready for interaction
      if (
        video.status !== StatusEnum.READY &&
        video.status !== StatusEnum.SAVED
      ) {
        throw new ForbiddenException('Video is not ready for interaction');
      }

      // Basic privacy check - detailed authorization handled by request managers
      if (video.privacy === PrivacyEnum.PRIVATE) {
        this.logger.debug('Video is private, user authorization required', {
          videoId: entityId,
          privacy: video.privacy,
        });
        // Allow validation to pass - request managers will handle user-specific checks
      }

      this.logger.debug('Video validation passed', {
        videoId: entityId,
        status: video.status,
        privacy: video.privacy,
      });
    } catch (error) {
      this.logger.debug('VideoEntityValidator: Validation failed', {
        entityId,
        error: error.message,
      });

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      // Wrap unexpected errors
      this.logger.error('VideoEntityValidator: Unexpected validation error', {
        entityId,
        error: error.message,
        stack: error.stack,
      });
      throw new NotFoundException('Video not found');
    }
  }

  /**
   * Get video information for notifications
   */
  async getEntityInfo(entityId: string): Promise<{
    id: string;
    ownerId: string;
    title?: string;
    thumbnail?: string;
    [key: string]: any;
  }> {
    try {
      // Use VideoProvider for data access
      const video = await this.videoProvider.findByIdWithRelations(entityId, [
        'user',
        'originalImageCompletion',
      ]);

      if (!video) {
        throw new NotFoundException('Video not found');
      }

      // Generate a title from the prompt or use a default
      const title = video.prompt
        ? video.prompt.length > 50
          ? `${video.prompt.substring(0, 50)}...`
          : video.prompt
        : 'Untitled Video';

      // Use the original image completion as thumbnail if available
      const thumbnail =
        video.originalImageCompletion?.imagePaths?.thumbnail ||
        video.originalImageCompletion?.imagePaths?.original ||
        null;

      return {
        id: video.id,
        ownerId: video.userId,
        title,
        thumbnail,
        status: video.status,
        privacy: video.privacy,
        createdAt: video.createdAt,
        likes: video.likes,
        comments: video.comments,
        isNsfw: video.isNsfw,
        hidePrompt: video.hidePrompt,
      };
    } catch (error) {
      this.logger.error('Failed to get video entity info', {
        entityId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Additional video-specific validation for user permissions
   */
  async validateUserCanInteract(
    entityId: string,
    userId: string,
  ): Promise<void> {
    try {
      // Use VideoProvider for data access
      const video = await this.videoProvider.findByIdWithRelations(entityId, [
        'user',
      ]);

      if (!video) {
        throw new NotFoundException('Video not found');
      }

      // Private videos can only be interacted with by the owner
      if (video.privacy === PrivacyEnum.PRIVATE && video.userId !== userId) {
        throw new ForbiddenException(
          'You cannot interact with this private video',
        );
      }
    } catch (error) {
      this.logger.error('Failed to validate user interaction permissions', {
        entityId,
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Check if a video is accessible to a user
   */
  async isVideoAccessibleToUser(
    entityId: string,
    userId?: string,
  ): Promise<boolean> {
    try {
      // Use VideoProvider for data access
      const video = await this.videoProvider.findOne(entityId);

      if (!video || video.deletedAt) {
        this.logger.debug('Video not found or deleted', { entityId });
        return false;
      }

      // Check if video is in a valid state for access
      if (
        video.status !== StatusEnum.READY &&
        video.status !== StatusEnum.SAVED
      ) {
        this.logger.debug('Video not in accessible state', {
          entityId,
          status: video.status,
        });
        return false;
      }

      // Public and licensed videos are accessible to everyone
      if (
        video.privacy === PrivacyEnum.PUBLIC ||
        video.privacy === PrivacyEnum.LICENSED
      ) {
        return true;
      }

      // Private videos are only accessible to the owner
      if (video.privacy === PrivacyEnum.PRIVATE) {
        const isOwner = userId === video.userId;
        this.logger.debug('Private video access check', {
          entityId,
          userId,
          ownerId: video.userId,
          isOwner,
        });
        return isOwner;
      }

      // Unknown privacy setting
      this.logger.warn('Unknown video privacy setting', {
        entityId,
        privacy: video.privacy,
      });
      return false;
    } catch (error) {
      this.logger.error('Failed to check video accessibility', {
        entityId,
        userId,
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }
}
