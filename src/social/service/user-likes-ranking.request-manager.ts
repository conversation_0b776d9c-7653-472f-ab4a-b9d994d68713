import { Injectable } from '@nestjs/common';
import { EntityLikeProvider } from './entity-like.provider';
import { EntityLikeResponseMapper } from './entity-like.response-mapper';
import { UserLikesRankingSearchRequest } from '../dto/user-likes-ranking.search.request';

@Injectable()
export class UserLikesRankingRequestManager {
  constructor(
    private entityLikeProvider: EntityLikeProvider,
    private entityLikeResponseMapper: EntityLikeResponseMapper,
  ) {}

  async generateUserLikesRanking(
    query: UserLikesRankingSearchRequest,
  ): Promise<any> {
    const start = new Date();

    const { page, limit, ...filters } = query;

    // Calculate time range based on filters
    if (filters.day) {
      start.setDate(start.getDate() - 1);
    } else if (filters.week) {
      start.setDate(start.getDate() - 7);
    } else if (filters.month) {
      start.setMonth(start.getMonth() - 1);
    } else if (filters.year) {
      start.setFullYear(start.getFullYear() - 1);
    } else {
      // Default to all time if no time filter is specified
      start.setFullYear(start.getFullYear() - 10); // Go back 10 years as "all time"
    }

    const entities = await this.entityLikeProvider.findUserLikesRanking(
      start.toISOString(),
      new Date().toISOString(),
      page,
      limit,
      filters.userId ?? null,
    );

    const totalCount = await this.entityLikeProvider.countUserLikesRanking(
      start.toISOString(),
      new Date().toISOString(),
      filters.userId ?? null,
    );

    // If filtering by specific user, return single result
    if (query.userId) {
      let entity = entities[0];

      if (!entity) {
        entity = {
          user_id: query.userId,
          likes_count: 0,
          rank: null,
        };
      }

      return {
        dtos: await this.entityLikeResponseMapper.mapUserLikesRank(entity),
        totalCount,
      };
    }

    // Return multiple results for ranking
    return {
      dtos: await this.entityLikeResponseMapper.mapUserLikesRankMultiple(
        entities,
      ),
      totalCount,
    };
  }
}
