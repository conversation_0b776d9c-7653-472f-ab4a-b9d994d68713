import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import {
  StatusEnum,
  PrivacyEnum,
} from '../../image-completion/entity/image-completion.entity';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import { EntityValidator } from './entity-validator.interface';

@Injectable()
export class ImageCompletionEntityValidator implements EntityValidator {
  constructor(
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly logger: Logger,
  ) {}

  /**
   * Validate that an image exists and can be commented on/liked
   */
  async validateExists(entityId: string): Promise<void> {
    try {
      this.logger.debug(
        'ImageCompletionEntityValidator: Validating image existence',
        {
          entityId,
        },
      );

      // Use ImageCompletionProvider for data access - minimal relations for performance
      const image = await this.imageCompletionProvider.findOne(entityId);

      if (!image) {
        throw new NotFoundException('Image not found');
      }

      // Check if image is soft deleted
      if (image.deletedAt) {
        throw new NotFoundException('Image has been deleted');
      }

      // Check if image is ready for interaction
      if (image.status !== StatusEnum.READY) {
        throw new ForbiddenException('Image is not ready for interaction');
      }

      // Check if image is private (only public images can be interacted with socially)
      if (image.privacy === PrivacyEnum.PRIVATE) {
        throw new ForbiddenException('Cannot interact with private images');
      }

      // Check if image is unsafe or NSFW (optional restriction)
      if (image.isUnsafe) {
        throw new ForbiddenException('Cannot interact with unsafe images');
      }

      this.logger.debug(
        'ImageCompletionEntityValidator: Image validation successful',
        {
          entityId,
          status: image.status,
          privacy: image.privacy,
        },
      );
    } catch (error) {
      this.logger.error('ImageCompletionEntityValidator: Validation failed', {
        entityId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get image information for notifications and responses
   */
  async getEntityInfo(entityId: string): Promise<{
    id: string;
    ownerId: string;
    title?: string;
    thumbnail?: string;
    status: string;
    privacy: string;
    createdAt: Date;
    likes: number;
    comments: number;
    isNsfw: boolean;
    hidePrompt: boolean;
    [key: string]: any;
  }> {
    try {
      this.logger.debug(
        'ImageCompletionEntityValidator: Getting image entity info',
        {
          entityId,
        },
      );

      // Get image with minimal relations for performance
      const image = await this.imageCompletionProvider.findOne(entityId);

      if (!image) {
        throw new NotFoundException('Image not found');
      }

      // Generate title from prompt or use default
      const title = image.hidePrompt
        ? 'Generated Image'
        : image.prompt?.substring(0, 100) || 'Generated Image';

      // Get thumbnail from image paths
      const thumbnail =
        image.imagePaths?.thumbnail ||
        image.imagePaths?.small ||
        image.imagePaths?.original ||
        null;

      return {
        id: image.id,
        ownerId: image.userId,
        title,
        thumbnail,
        status: image.status,
        privacy: image.privacy,
        createdAt: image.createdAt,
        likes: image.likes || 0,
        comments: image.comments || 0,
        isNsfw: image.isNsfw || false,
        hidePrompt: image.hidePrompt || false,
        prompt: image.hidePrompt ? null : image.prompt,
        imagePaths: image.imagePaths,
        hasWatermark: image.hasWatermark,
        generationSettings: image.generationSettings,
      };
    } catch (error) {
      this.logger.error('Failed to get image entity info', {
        entityId,
        error: error.message,
      });
      throw error;
    }
  }
}
