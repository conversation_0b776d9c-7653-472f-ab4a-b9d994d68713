import { Injectable } from '@nestjs/common';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { EntityLikeDto } from '../dto/entity-like.dto';
import { EntityValidatorService } from './entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { UserResponseMapper } from '../../user/service/response-mapper';
import { PublicUserDto } from '../../user/dto/public.user.dto';
import { UserLikesRankDto } from '../dto/user-likes-rank.dto';
import {
  EntityLikeCursorResponseDto,
  EntityLikeCursorPaginationDto,
} from '../dto/entity-like-cursor.response';

@Injectable()
export class EntityLikeResponseMapper {
  constructor(
    private userProvider: UserProvider,
    private userResponseMapper: UserResponseMapper,
    private validatorService: EntityValidatorService,
  ) {}

  /**
   * Map a single like entity to DTO
   */
  async map(
    like: EntityLikeEntity,
    includeEntity = false,
  ): Promise<EntityLikeDto> {
    const dto = new EntityLikeDto();

    dto.id = like.id;
    dto.entityType = like.entityType;
    dto.entityId = like.entityId;
    dto.createdAt = like.createdAt;

    // Map user information
    if (like.user) {
      dto.user = {
        id: like.user.id,
        name: like.user.name,
        username: like.user.username,
        profilePicture: like.user.profilePicture,
        description: like.user.description,
        website: like.user.website,
        socialMediaAccounts: like.user.socialMediaAccounts,
        imagesGenerated: like.user.imagesGenerated,
        imagesAvailable: like.user.imagesAvailable,
        modelsAvailable: like.user.modelsAvailable,
        followersCount: like.user.followersCount,
        followingCount: like.user.followingCount,
        isVerified: like.user.isVerified,
      };
    } else if (like.userId) {
      // Fetch user if not included in the entity
      const user = await this.userProvider.get(like.userId);
      if (user) {
        dto.user = {
          id: user.id,
          name: user.name,
          username: user.username,
          profilePicture: user.profilePicture,
          description: user.description,
          website: user.website,
          socialMediaAccounts: user.socialMediaAccounts,
          imagesGenerated: user.imagesGenerated,
          imagesAvailable: user.imagesAvailable,
          modelsAvailable: user.modelsAvailable,
          followersCount: user.followersCount,
          followingCount: user.followingCount,
          isVerified: user.isVerified,
        };
      }
    }

    // Include entity information if requested
    if (includeEntity) {
      try {
        const entityInfo = await this.validatorService.getEntityInfo(
          like.entityType,
          like.entityId,
        );
        dto.entity = entityInfo;
      } catch (error) {
        // Entity might be deleted or inaccessible, don't include it
        dto.entity = null;
      }
    }

    return dto;
  }

  /**
   * Map multiple like entities to DTOs
   */
  async mapMultiple(
    likes: EntityLikeEntity[],
    includeEntity = false,
  ): Promise<EntityLikeDto[]> {
    if (likes.length === 0) {
      return [];
    }

    // Map all likes
    const mappedLikes = await Promise.all(
      likes.map((like) => this.map(like, includeEntity)),
    );

    return mappedLikes;
  }

  /**
   * Map likes with pagination metadata
   */
  async mapWithPagination(
    likes: EntityLikeEntity[],
    total: number,
    page: number,
    limit: number,
    includeEntity = false,
  ): Promise<{
    data: EntityLikeDto[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const data = await this.mapMultiple(likes, includeEntity);
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Map like statistics
   */
  mapStats(stats: { entityId: string; likeCount: number }[]): {
    entityId: string;
    likeCount: number;
  }[] {
    return stats.map((stat) => ({
      entityId: stat.entityId,
      likeCount: stat.likeCount,
    }));
  }

  /**
   * Map top liked entities
   */
  mapTopLiked(topLiked: { entityId: string; likeCount: number }[]): {
    entityId: string;
    likeCount: number;
  }[] {
    return topLiked.map((item) => ({
      entityId: item.entityId,
      likeCount: item.likeCount,
    }));
  }

  /**
   * Map user likes ranking data to DTO (matches image_completions/likes-ranking format)
   */
  async mapUserLikesRank(
    entity: any,
    mapRank = false,
  ): Promise<UserLikesRankDto> {
    const user = await this.userProvider.get(entity.user_id);

    const dto = new UserLikesRankDto();

    if (mapRank) {
      dto.rank = entity.rank;
    }

    dto.user = this.userResponseMapper.mapPublic(user);
    dto.userId = user.id;
    dto.likes = entity.likes_count;

    return dto;
  }

  /**
   * Map multiple user likes ranking data to DTOs
   */
  async mapUserLikesRankMultiple(entities: any[]): Promise<UserLikesRankDto[]> {
    return Promise.all(
      entities.map((entity) => this.mapUserLikesRank(entity, true)),
    );
  }

  /**
   * Map likes with cursor-based pagination
   */
  async mapWithCursorPagination(
    likes: EntityLikeEntity[],
    limit: number,
    includeEntity = false,
    sortBy = 'createdAt',
  ): Promise<EntityLikeCursorResponseDto> {
    // Check if we have more items than requested (we fetched limit + 1)
    const hasMore = likes.length > limit;

    // Remove the extra item if present
    const itemsToMap = hasMore ? likes.slice(0, limit) : likes;

    // Map the items
    const mappedItems = await this.mapMultiple(itemsToMap, includeEntity);

    // Generate cursor and pagination metadata
    let nextCursor: string | undefined;
    let nextCursorTimestamp: string | undefined;

    if (hasMore && itemsToMap.length > 0) {
      const lastItem = itemsToMap[itemsToMap.length - 1];

      if (sortBy === 'createdAt' && lastItem.createdAt) {
        nextCursor = lastItem.createdAt.getTime().toString();
        nextCursorTimestamp = lastItem.createdAt.toISOString();
      } else if (lastItem[sortBy] !== undefined && lastItem[sortBy] !== null) {
        // For other sort fields, use the field value as cursor
        nextCursor = lastItem[sortBy].toString();
        nextCursorTimestamp = lastItem.createdAt?.toISOString();
      }
    }

    const pagination: EntityLikeCursorPaginationDto = {
      count: mappedItems.length,
      hasMore,
      nextCursor,
      nextCursorTimestamp,
    };

    return {
      items: mappedItems,
      pagination,
    };
  }
}
