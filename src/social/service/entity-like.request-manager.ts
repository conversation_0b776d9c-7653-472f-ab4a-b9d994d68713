import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { EntityLikeManager } from './entity-like.manager';
import { EntityLikeProvider } from './entity-like.provider';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { EntityValidator } from './entity-validator.interface';

@Injectable()
export class EntityLikeRequestManager {
  constructor(
    private manager: EntityLikeManager,
    private provider: EntityLikeProvider,
  ) {}

  /**
   * Like an entity with duplicate prevention
   */
  async likeEntity(
    entityType: string,
    entityId: string,
    userId: string,
    entityValidator: EntityValidator,
  ): Promise<EntityLikeEntity> {
    // Validate inputs
    this.validateLikeInputs(entityType, entityId, userId);

    // Check if entity exists and can be liked
    await this.validateEntityForLike(entityType, entityId, entityValidator);

    // Check for existing like
    const existingLike = await this.provider.getUserLikeForEntity(
      entityType,
      entityId,
      userId,
    );

    if (existingLike) {
      throw new ConflictException('You have already liked this entity');
    }

    // Create the like
    return this.manager.likeEntity(
      entityType,
      entityId,
      userId,
      entityValidator,
    );
  }

  /**
   * Unlike an entity
   */
  async unlikeEntity(
    entityType: string,
    entityId: string,
    userId: string,
    entityValidator: EntityValidator,
  ): Promise<void> {
    // Validate inputs
    this.validateLikeInputs(entityType, entityId, userId);

    // Find existing like
    const existingLike = await this.provider.getUserLikeForEntity(
      entityType,
      entityId,
      userId,
    );

    if (!existingLike) {
      throw new NotFoundException('You have not liked this entity');
    }

    // Remove the like
    await this.manager.unlikeEntity(existingLike, entityValidator);
  }

  /**
   * Toggle like status (like if not liked, unlike if liked)
   */
  async toggleEntityLike(
    entityType: string,
    entityId: string,
    userId: string,
    entityValidator: EntityValidator,
  ): Promise<{ liked: boolean; like?: EntityLikeEntity }> {
    // Validate inputs
    this.validateLikeInputs(entityType, entityId, userId);

    // Check if entity exists and can be liked
    await this.validateEntityForLike(entityType, entityId, entityValidator);

    // Check for existing like
    const existingLike = await this.provider.getUserLikeForEntity(
      entityType,
      entityId,
      userId,
    );

    // Toggle like status
    return this.manager.toggleEntityLike(
      entityType,
      entityId,
      userId,
      existingLike,
      entityValidator,
    );
  }

  /**
   * Get likes for an entity with pagination
   */
  async getLikesForEntity(
    entityType: string,
    entityId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityLikeEntity[]> {
    // Validate pagination parameters
    this.validatePaginationParams(page, limit);

    return this.provider.findLikesByEntity(
      entityType,
      entityId,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }

  /**
   * Count likes for an entity
   */
  async countLikesForEntity(
    entityType: string,
    entityId: string,
  ): Promise<number> {
    return this.provider.countLikesByEntity(entityType, entityId);
  }

  /**
   * Get likes by user
   */
  async getLikesByUser(
    userId: string,
    page: number,
    limit: number,
    entityType?: string,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityLikeEntity[]> {
    this.validatePaginationParams(page, limit);

    if (entityType) {
      return this.provider.findLikesByUserAndEntityType(
        userId,
        entityType,
        page,
        limit,
        sortBy,
        sortOrder,
      );
    } else {
      return this.provider.findLikesByUser(
        userId,
        page,
        limit,
        sortBy,
        sortOrder,
      );
    }
  }

  /**
   * Check if user has liked an entity
   */
  async hasUserLikedEntity(
    entityType: string,
    entityId: string,
    userId: string,
  ): Promise<boolean> {
    return this.provider.hasUserLikedEntity(entityType, entityId, userId);
  }

  /**
   * Get like statistics for multiple entities
   */
  async getLikeStatsForEntities(
    entityType: string,
    entityIds: string[],
  ): Promise<{ entityId: string; likeCount: number }[]> {
    if (entityIds.length === 0) {
      return [];
    }

    if (entityIds.length > 100) {
      throw new BadRequestException(
        'Cannot get stats for more than 100 entities at once',
      );
    }

    return this.provider.getLikeStatsForEntities(entityType, entityIds);
  }

  /**
   * Get top liked entities
   */
  async getTopLikedEntities(
    entityType: string,
    limit: number,
    timeframe?: 'day' | 'week' | 'month' | 'year',
  ): Promise<{ entityId: string; likeCount: number }[]> {
    if (limit < 1 || limit > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }

    return this.provider.getTopLikedEntities(entityType, limit, timeframe);
  }

  /**
   * Validate like inputs
   */
  private validateLikeInputs(
    entityType: string,
    entityId: string,
    userId: string,
  ): void {
    if (!entityType || entityType.trim().length === 0) {
      throw new BadRequestException('Entity type is required');
    }

    if (!entityId || entityId.trim().length === 0) {
      throw new BadRequestException('Entity ID is required');
    }

    if (!userId || userId.trim().length === 0) {
      throw new BadRequestException('User ID is required');
    }

    // Validate UUID format for entityId and userId
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

    if (!uuidRegex.test(entityId)) {
      throw new BadRequestException('Invalid entity ID format');
    }

    if (!uuidRegex.test(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    // Validate entity type format
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(entityType)) {
      throw new BadRequestException('Invalid entity type format');
    }
  }

  /**
   * Validate entity for liking
   */
  private async validateEntityForLike(
    entityType: string,
    entityId: string,
    entityValidator: EntityValidator,
  ): Promise<void> {
    try {
      await entityValidator.validateExists(entityId);
    } catch (error) {
      throw new BadRequestException(
        `Cannot like this ${entityType}: ${error.message}`,
      );
    }
  }

  /**
   * Validate pagination parameters
   */
  private validatePaginationParams(page: number, limit: number): void {
    if (page < 1) {
      throw new BadRequestException('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }
  }
}
