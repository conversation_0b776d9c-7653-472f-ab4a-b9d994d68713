import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger } from 'nestjs-pino';
import { EntityLikeProvider } from './entity-like.provider';
import { EntityLikeEntity } from '../entity/entity-like.entity';

describe('EntityLikeProvider', () => {
  let provider: EntityLikeProvider;
  let repository: Repository<EntityLikeEntity>;
  let logger: Logger;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    count: jest.fn(),
    countBy: jest.fn(),
    exists: jest.fn(),
    createQueryBuilder: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    softRemove: jest.fn(),
  };

  const mockLogger = {
    debug: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityLikeProvider,
        {
          provide: getRepositoryToken(EntityLikeEntity),
          useValue: mockRepository,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    provider = module.get<EntityLikeProvider>(EntityLikeProvider);
    repository = module.get<Repository<EntityLikeEntity>>(
      getRepositoryToken(EntityLikeEntity),
    );
    logger = module.get<Logger>(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findLikesByEntity', () => {
    it('should find likes for a specific entity', async () => {
      const mockLikes = [
        {
          id: '1',
          entityType: 'video',
          entityId: 'video-1',
          userId: 'user-1',
          user: { id: 'user-1', username: 'testuser1' },
        },
        {
          id: '2',
          entityType: 'video',
          entityId: 'video-1',
          userId: 'user-2',
          user: { id: 'user-2', username: 'testuser2' },
        },
      ];

      mockRepository.find.mockResolvedValue(mockLikes);

      const result = await provider.findLikesByEntity(
        'video',
        'video-1',
        1,
        10,
      );

      expect(result).toEqual(mockLikes);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { entityType: 'video', entityId: 'video-1' },
        relations: ['user'],
        skip: 0,
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });

    it('should handle pagination correctly', async () => {
      mockRepository.find.mockResolvedValue([]);

      await provider.findLikesByEntity('video', 'video-1', 3, 5);

      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { entityType: 'video', entityId: 'video-1' },
        relations: ['user'],
        skip: 10, // (page - 1) * limit = (3 - 1) * 5
        take: 5,
        order: { createdAt: 'DESC' },
      });
    });
  });

  describe('countLikesByEntity', () => {
    it('should count likes for a specific entity', async () => {
      mockRepository.countBy.mockResolvedValue(25);

      const result = await provider.countLikesByEntity('video', 'video-1');

      expect(result).toBe(25);
      expect(mockRepository.countBy).toHaveBeenCalledWith({
        entityType: 'video',
        entityId: 'video-1',
      });
    });

    it('should return 0 when no likes exist', async () => {
      mockRepository.countBy.mockResolvedValue(0);

      const result = await provider.countLikesByEntity('model', 'model-1');

      expect(result).toBe(0);
    });
  });

  describe('findLikesByUser', () => {
    it('should find likes by user across all entities', async () => {
      const mockLikes = [
        {
          id: '1',
          entityType: 'video',
          entityId: 'video-1',
          userId: 'user-1',
          user: { id: 'user-1', username: 'testuser' },
        },
        {
          id: '2',
          entityType: 'model',
          entityId: 'model-1',
          userId: 'user-1',
          user: { id: 'user-1', username: 'testuser' },
        },
      ];

      mockRepository.find.mockResolvedValue(mockLikes);

      const result = await provider.findLikesByUser('user-1', 1, 10);

      expect(result).toEqual(mockLikes);
      expect(mockRepository.find).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        relations: ['user'],
        skip: 0,
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });
  });

  describe('hasUserLikedEntity', () => {
    it('should return true if user has liked entity', async () => {
      mockRepository.findOne.mockResolvedValue({ id: '1' });

      const result = await provider.hasUserLikedEntity(
        'video',
        'video-1',
        'user-1',
      );

      expect(result).toBe(true);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { entityType: 'video', entityId: 'video-1', userId: 'user-1' },
        relations: ['user'],
      });
    });

    it('should return false if user has not liked entity', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await provider.hasUserLikedEntity(
        'video',
        'video-1',
        'user-1',
      );

      expect(result).toBe(false);
    });
  });

  describe('getUserLikeForEntity', () => {
    it('should return user like for entity if exists', async () => {
      const mockLike = {
        id: '1',
        entityType: 'video',
        entityId: 'video-1',
        userId: 'user-1',
      };

      mockRepository.findOne.mockResolvedValue(mockLike);

      const result = await provider.getUserLikeForEntity(
        'video',
        'video-1',
        'user-1',
      );

      expect(result).toEqual(mockLike);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { entityType: 'video', entityId: 'video-1', userId: 'user-1' },
        relations: ['user'],
      });
    });

    it('should return null if user has not liked entity', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await provider.getUserLikeForEntity(
        'video',
        'video-1',
        'user-1',
      );

      expect(result).toBeNull();
    });
  });

  describe('getTopLikedEntities', () => {
    it('should return top liked entities for a type', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn(),
      };

      const mockResults = [
        { entityId: 'video-1', likeCount: '25' },
        { entityId: 'video-2', likeCount: '15' },
      ];

      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockQueryBuilder.getRawMany.mockResolvedValue(mockResults);

      const result = await provider.getTopLikedEntities('video', 10);

      expect(result).toEqual([
        { entityId: 'video-1', likeCount: 25 },
        { entityId: 'video-2', likeCount: 15 },
      ]);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith(
        'entityLike',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'entityLike.entityType = :entityType',
        { entityType: 'video' },
      );
    });
  });

  describe('error handling', () => {
    it('should handle repository errors gracefully', async () => {
      const error = new Error('Database connection failed');
      mockRepository.find.mockRejectedValue(error);

      await expect(
        provider.findLikesByEntity('video', 'video-1', 1, 10),
      ).rejects.toThrow('Database connection failed');
    });
  });
});
