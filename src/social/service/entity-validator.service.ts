import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { EntityValidator } from './entity-validator.interface';

@Injectable()
export class EntityValidatorService {
  private validators = new Map<string, EntityValidator>();

  constructor(private logger: Logger) {}

  /**
   * Register a validator for a specific entity type
   */
  registerValidator(entityType: string, validator: EntityValidator): void {
    const normalizedType = this.normalizeEntityType(entityType);

    if (this.validators.has(normalizedType)) {
      this.logger.warn('Overriding existing validator for entity type', {
        entityType: normalizedType,
      });
    }

    this.validators.set(normalizedType, validator);

    this.logger.log('Registered entity validator', {
      entityType: normalizedType,
    });
  }

  /**
   * Get validator for a specific entity type
   */
  getValidator(entityType: string): EntityValidator {
    const normalizedType = this.normalizeEntityType(entityType);
    const validator = this.validators.get(normalizedType);

    if (!validator) {
      throw new BadRequestException(
        `No validator registered for entity type: ${entityType}`,
      );
    }

    return validator;
  }

  /**
   * Check if a validator is registered for an entity type
   */
  hasValidator(entityType: string): boolean {
    const normalizedType = this.normalizeEntityType(entityType);
    return this.validators.has(normalizedType);
  }

  /**
   * Get all registered entity types
   */
  getRegisteredEntityTypes(): string[] {
    return Array.from(this.validators.keys());
  }

  /**
   * Validate that an entity exists and can be interacted with
   */
  async validateEntityExists(
    entityType: string,
    entityId: string,
  ): Promise<void> {
    const validator = this.getValidator(entityType);

    try {
      await validator.validateExists(entityId);
    } catch (error) {
      this.logger.error('Entity validation failed', {
        entityType,
        entityId,
        error: error.message,
      });
      throw new NotFoundException(
        `${entityType} with ID ${entityId} not found or cannot be accessed`,
      );
    }
  }

  /**
   * Get entity information for notifications
   */
  async getEntityInfo(
    entityType: string,
    entityId: string,
  ): Promise<{
    id: string;
    ownerId: string;
    title?: string;
    thumbnail?: string;
    [key: string]: any;
  }> {
    const validator = this.getValidator(entityType);

    try {
      return await validator.getEntityInfo(entityId);
    } catch (error) {
      this.logger.error('Failed to get entity info', {
        entityType,
        entityId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Create a validator instance for a specific entity
   * This is used by request managers to get a validator that's bound to a specific entity type
   */
  createEntityValidator(entityType: string): EntityValidator {
    const baseValidator = this.getValidator(entityType);

    // Return a wrapper that includes the entity type in all calls
    return {
      validateExists: (entityId: string) =>
        baseValidator.validateExists(entityId),
      getEntityInfo: (entityId: string) =>
        baseValidator.getEntityInfo(entityId),
    };
  }

  /**
   * Unregister a validator (for testing or dynamic configuration)
   */
  unregisterValidator(entityType: string): boolean {
    const normalizedType = this.normalizeEntityType(entityType);
    const existed = this.validators.has(normalizedType);

    if (existed) {
      this.validators.delete(normalizedType);
      this.logger.log('Unregistered entity validator', {
        entityType: normalizedType,
      });
    }

    return existed;
  }

  /**
   * Clear all validators (for testing)
   */
  clearAllValidators(): void {
    this.validators.clear();
    this.logger.log('Cleared all entity validators');
  }

  /**
   * Normalize entity type to lowercase for consistent lookup
   */
  private normalizeEntityType(entityType: string): string {
    return entityType.toLowerCase().trim();
  }
}
