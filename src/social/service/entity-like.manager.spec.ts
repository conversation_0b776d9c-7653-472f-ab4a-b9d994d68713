import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, QueryBuilder } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityLikeManager } from './entity-like.manager';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { EntityValidator } from './entity-validator.interface';

describe('EntityLikeManager', () => {
  let manager: EntityLikeManager;
  let repository: jest.Mocked<Repository<EntityLikeEntity>>;
  let eventEmitter: jest.Mocked<EventEmitter2>;
  let logger: jest.Mocked<Logger>;
  let entityValidator: jest.Mocked<EntityValidator>;

  const createMockLike = (
    overrides: Partial<EntityLikeEntity> = {},
  ): EntityLikeEntity =>
    ({
      id: 'like-1',
      entityType: 'video',
      entityId: 'video-123',
      userId: 'user-456',
      createdAt: new Date(),
      deletedAt: null,
      user: null,
      ...overrides,
    } as EntityLikeEntity);

  beforeEach(async () => {
    const mockQueryBuilder = {
      softDelete: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ affected: 0 }),
    };

    const mockRepository = {
      save: jest.fn(),
      softDelete: jest.fn(),
      softRemove: jest.fn(),
      create: jest.fn(),
      findOne: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    };

    const mockEventEmitter = {
      emit: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      debug: jest.fn(),
      error: jest.fn(),
    };

    entityValidator = {
      validateExists: jest.fn(),
      getEntityInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityLikeManager,
        {
          provide: getRepositoryToken(EntityLikeEntity),
          useValue: mockRepository,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    manager = module.get<EntityLikeManager>(EntityLikeManager);
    repository = module.get(getRepositoryToken(EntityLikeEntity));
    eventEmitter = module.get(EventEmitter2);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('likeEntity', () => {
    it('should like an entity successfully', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const userId = 'user-456';
      const savedLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      repository.save.mockResolvedValue(savedLike);

      // Act
      const result = await manager.likeEntity(
        entityType,
        entityId,
        userId,
        entityValidator,
      );

      // Assert
      expect(entityValidator.validateExists).toHaveBeenCalledWith(entityId);
      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          entityType,
          entityId,
          userId,
        }),
      );
      expect(eventEmitter.emit).toHaveBeenCalledWith('entity.liked', {
        likeId: savedLike.id,
        entityType,
        entityId,
        userId,
      });
      expect(logger.log).toHaveBeenCalledWith('Entity liked', {
        likeId: savedLike.id,
        entityType,
        entityId,
        userId,
      });
      expect(result).toBe(savedLike);
    });

    it('should throw error when entity validation fails', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const userId = 'user-456';
      const validationError = new Error('Entity not found');

      entityValidator.validateExists.mockRejectedValue(validationError);

      // Act & Assert
      await expect(
        manager.likeEntity(entityType, entityId, userId, entityValidator),
      ).rejects.toThrow(validationError);
      expect(repository.save).not.toHaveBeenCalled();
      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });
  });

  describe('unlikeEntity', () => {
    it('should unlike an entity successfully', async () => {
      // Arrange
      const like = createMockLike();
      repository.softDelete.mockResolvedValue({
        affected: 1,
        raw: {},
        generatedMaps: [],
      });

      // Act
      await manager.unlikeEntity(like, entityValidator);

      // Assert
      expect(repository.softRemove).toHaveBeenCalledWith(like);
      expect(eventEmitter.emit).toHaveBeenCalledWith('entity.unliked', {
        likeId: like.id,
        entityType: like.entityType,
        entityId: like.entityId,
        userId: like.userId,
      });
      expect(logger.log).toHaveBeenCalledWith('Entity unliked', {
        likeId: like.id,
        entityType: like.entityType,
        entityId: like.entityId,
        userId: like.userId,
      });
    });
  });

  describe('toggleEntityLike', () => {
    it('should like entity when no existing like', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const userId = 'user-456';
      const savedLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      repository.save.mockResolvedValue(savedLike);

      // Act
      const result = await manager.toggleEntityLike(
        entityType,
        entityId,
        userId,
        null,
        entityValidator,
      );

      // Assert
      expect(result).toEqual({ liked: true, like: savedLike });
      expect(repository.save).toHaveBeenCalled();
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'entity.liked',
        expect.any(Object),
      );
    });

    it('should unlike entity when existing like provided', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const userId = 'user-456';
      const existingLike = createMockLike();

      repository.softDelete.mockResolvedValue({
        affected: 1,
        raw: {},
        generatedMaps: [],
      });

      // Act
      const result = await manager.toggleEntityLike(
        entityType,
        entityId,
        userId,
        existingLike,
        entityValidator,
      );

      // Assert
      expect(result).toEqual({ liked: false });
      expect(repository.softRemove).toHaveBeenCalledWith(existingLike);
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'entity.unliked',
        expect.any(Object),
      );
    });
  });

  describe('batchLikeEntities', () => {
    it('should create multiple likes successfully', async () => {
      // Arrange
      const likesData = [
        { entityType: 'video', entityId: 'video-1', userId: 'user-1' },
        { entityType: 'video', entityId: 'video-2', userId: 'user-1' },
      ];
      const savedLikes = [
        createMockLike({ id: 'like-1' }),
        createMockLike({ id: 'like-2' }),
      ];

      entityValidator.validateExists.mockResolvedValue();
      repository.save
        .mockResolvedValueOnce(savedLikes[0])
        .mockResolvedValueOnce(savedLikes[1]);

      // Act
      const result = await manager.batchLikeEntities(
        likesData,
        entityValidator,
      );

      // Assert
      expect(result).toHaveLength(2);
      expect(repository.save).toHaveBeenCalledTimes(2);
      expect(eventEmitter.emit).toHaveBeenCalledTimes(2);
    });

    it('should continue processing when one like fails', async () => {
      // Arrange
      const likesData = [
        { entityType: 'video', entityId: 'video-1', userId: 'user-1' },
        { entityType: 'video', entityId: 'video-2', userId: 'user-1' },
      ];
      const savedLike = createMockLike({ id: 'like-2' });

      entityValidator.validateExists
        .mockRejectedValueOnce(new Error('First entity not found'))
        .mockResolvedValueOnce();
      repository.save.mockResolvedValueOnce(savedLike);

      // Act
      const result = await manager.batchLikeEntities(
        likesData,
        entityValidator,
      );

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toBe(savedLike);
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to create like in batch operation',
        expect.objectContaining({
          error: 'First entity not found',
          likeData: likesData[0],
        }),
      );
    });
  });

  describe('removeAllLikesForEntity', () => {
    it('should remove all likes for an entity', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'video-123';
      const mockQueryBuilder = repository.createQueryBuilder();
      (mockQueryBuilder.execute as jest.Mock).mockResolvedValue({
        affected: 5,
      });

      // Act
      const result = await manager.removeAllLikesForEntity(
        entityType,
        entityId,
      );

      // Assert
      expect(result).toBe(5);
      expect(mockQueryBuilder.softDelete).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'entityType = :entityType',
        { entityType },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'entityId = :entityId',
        { entityId },
      );
      expect(logger.log).toHaveBeenCalledWith('Removed all likes for entity', {
        entityType,
        entityId,
        deletedCount: 5,
      });
    });
  });

  describe('removeAllLikesByUser', () => {
    it('should remove all likes by a user', async () => {
      // Arrange
      const userId = 'user-123';
      const mockQueryBuilder = repository.createQueryBuilder();
      (mockQueryBuilder.execute as jest.Mock).mockResolvedValue({
        affected: 3,
      });

      // Act
      const result = await manager.removeAllLikesByUser(userId);

      // Assert
      expect(result).toBe(3);
      expect(mockQueryBuilder.softDelete).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('userId = :userId', {
        userId,
      });
      expect(logger.log).toHaveBeenCalledWith('Removed all likes by user', {
        userId,
        deletedCount: 3,
      });
    });
  });

  describe('validateEntity', () => {
    it('should validate valid like entity', async () => {
      // Arrange
      const like = createMockLike();

      // Act & Assert - should not throw
      await expect(
        (manager as any).validateEntity(like),
      ).resolves.not.toThrow();
    });

    it('should throw error for missing entity type', async () => {
      // Arrange
      const like = createMockLike({ entityType: '' });

      // Act & Assert
      await expect((manager as any).validateEntity(like)).rejects.toThrow(
        'Entity type is required',
      );
    });

    it('should throw error for missing entity ID', async () => {
      // Arrange
      const like = createMockLike({ entityId: '' });

      // Act & Assert
      await expect((manager as any).validateEntity(like)).rejects.toThrow(
        'Entity ID is required',
      );
    });

    it('should throw error for missing user ID', async () => {
      // Arrange
      const like = createMockLike({ userId: '' });

      // Act & Assert
      await expect((manager as any).validateEntity(like)).rejects.toThrow(
        'User ID is required',
      );
    });
  });

  describe('preSave', () => {
    it('should normalize entity type to lowercase', async () => {
      // Arrange
      const like = createMockLike({ entityType: 'VIDEO' });

      // Act
      await (manager as any).preSave(like);

      // Assert
      expect(like.entityType).toBe('video');
    });

    it('should handle null entity type', async () => {
      // Arrange
      const like = createMockLike({ entityType: null });

      // Act & Assert - should not throw
      await expect((manager as any).preSave(like)).resolves.not.toThrow();
    });
  });
});
