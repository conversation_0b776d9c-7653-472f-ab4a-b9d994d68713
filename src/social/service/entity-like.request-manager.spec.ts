import { Test, TestingModule } from '@nestjs/testing';
import {
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { EntityLikeRequestManager } from './entity-like.request-manager';
import { EntityLikeManager } from './entity-like.manager';
import { EntityLikeProvider } from './entity-like.provider';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { EntityValidator } from './entity-validator.interface';

describe('EntityLikeRequestManager', () => {
  let requestManager: EntityLikeRequestManager;
  let manager: jest.Mocked<EntityLikeManager>;
  let provider: jest.Mocked<EntityLikeProvider>;
  let entityValidator: jest.Mocked<EntityValidator>;

  const createMockLike = (
    overrides: Partial<EntityLikeEntity> = {},
  ): EntityLikeEntity =>
    ({
      id: '123e4567-e89b-12d3-a456-426614174000',
      entityType: 'video',
      entityId: '123e4567-e89b-12d3-a456-426614174001',
      userId: '123e4567-e89b-12d3-a456-426614174002',
      createdAt: new Date(),
      deletedAt: null,
      user: null,
      ...overrides,
    } as EntityLikeEntity);

  beforeEach(async () => {
    const mockManager = {
      likeEntity: jest.fn(),
      unlikeEntity: jest.fn(),
      toggleEntityLike: jest.fn(),
    };

    const mockProvider = {
      getUserLikeForEntity: jest.fn(),
      findLikesByEntity: jest.fn(),
      countLikesByEntity: jest.fn(),
      findLikesByUser: jest.fn(),
    };

    entityValidator = {
      validateExists: jest.fn(),
      getEntityInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityLikeRequestManager,
        {
          provide: EntityLikeManager,
          useValue: mockManager,
        },
        {
          provide: EntityLikeProvider,
          useValue: mockProvider,
        },
      ],
    }).compile();

    requestManager = module.get<EntityLikeRequestManager>(
      EntityLikeRequestManager,
    );
    manager = module.get(EntityLikeManager);
    provider = module.get(EntityLikeProvider);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('likeEntity', () => {
    it('should like an entity successfully', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const savedLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      provider.getUserLikeForEntity.mockResolvedValue(null);
      manager.likeEntity.mockResolvedValue(savedLike);

      // Act
      const result = await requestManager.likeEntity(
        entityType,
        entityId,
        userId,
        entityValidator,
      );

      // Assert
      expect(entityValidator.validateExists).toHaveBeenCalledWith(entityId);
      expect(provider.getUserLikeForEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
        userId,
      );
      expect(manager.likeEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
        userId,
        entityValidator,
      );
      expect(result).toBe(savedLike);
    });

    it('should throw ConflictException when entity is already liked', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const existingLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      provider.getUserLikeForEntity.mockResolvedValue(existingLike);

      // Act & Assert
      await expect(
        requestManager.likeEntity(
          entityType,
          entityId,
          userId,
          entityValidator,
        ),
      ).rejects.toThrow(ConflictException);
      await expect(
        requestManager.likeEntity(
          entityType,
          entityId,
          userId,
          entityValidator,
        ),
      ).rejects.toThrow('You have already liked this entity');
      expect(manager.likeEntity).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid inputs', async () => {
      // Act & Assert
      await expect(
        requestManager.likeEntity(
          '',
          '123e4567-e89b-12d3-a456-426614174001',
          '123e4567-e89b-12d3-a456-426614174002',
          entityValidator,
        ),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.likeEntity(
          'video',
          '',
          '123e4567-e89b-12d3-a456-426614174002',
          entityValidator,
        ),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.likeEntity(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          '',
          entityValidator,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('unlikeEntity', () => {
    it('should unlike an entity successfully', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const existingLike = createMockLike();

      provider.getUserLikeForEntity.mockResolvedValue(existingLike);
      manager.unlikeEntity.mockResolvedValue();

      // Act
      await requestManager.unlikeEntity(
        entityType,
        entityId,
        userId,
        entityValidator,
      );

      // Assert
      expect(provider.getUserLikeForEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
        userId,
      );
      expect(manager.unlikeEntity).toHaveBeenCalledWith(
        existingLike,
        entityValidator,
      );
    });

    it('should throw NotFoundException when entity is not liked', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';

      provider.getUserLikeForEntity.mockResolvedValue(null);

      // Act & Assert
      await expect(
        requestManager.unlikeEntity(
          entityType,
          entityId,
          userId,
          entityValidator,
        ),
      ).rejects.toThrow(NotFoundException);
      await expect(
        requestManager.unlikeEntity(
          entityType,
          entityId,
          userId,
          entityValidator,
        ),
      ).rejects.toThrow('You have not liked this entity');
      expect(manager.unlikeEntity).not.toHaveBeenCalled();
    });
  });

  describe('toggleEntityLike', () => {
    it('should like entity when not already liked', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const savedLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      provider.getUserLikeForEntity.mockResolvedValue(null);
      manager.toggleEntityLike.mockResolvedValue({
        liked: true,
        like: savedLike,
      });

      // Act
      const result = await requestManager.toggleEntityLike(
        entityType,
        entityId,
        userId,
        entityValidator,
      );

      // Assert
      expect(result).toEqual({ liked: true, like: savedLike });
      expect(manager.toggleEntityLike).toHaveBeenCalledWith(
        entityType,
        entityId,
        userId,
        null,
        entityValidator,
      );
    });

    it('should unlike entity when already liked', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const existingLike = createMockLike();

      entityValidator.validateExists.mockResolvedValue();
      provider.getUserLikeForEntity.mockResolvedValue(existingLike);
      manager.toggleEntityLike.mockResolvedValue({ liked: false });

      // Act
      const result = await requestManager.toggleEntityLike(
        entityType,
        entityId,
        userId,
        entityValidator,
      );

      // Assert
      expect(result).toEqual({ liked: false });
      expect(manager.toggleEntityLike).toHaveBeenCalledWith(
        entityType,
        entityId,
        userId,
        existingLike,
        entityValidator,
      );
    });
  });

  describe('getLikesForEntity', () => {
    it('should get likes for entity with valid pagination', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const page = 1;
      const limit = 10;
      const likes = [createMockLike()];

      provider.findLikesByEntity.mockResolvedValue(likes);

      // Act
      const result = await requestManager.getLikesForEntity(
        entityType,
        entityId,
        page,
        limit,
      );

      // Assert
      expect(provider.findLikesByEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
        page,
        limit,
        'createdAt',
        'DESC',
      );
      expect(result).toBe(likes);
    });

    it('should throw BadRequestException for invalid page', async () => {
      // Act & Assert
      await expect(
        requestManager.getLikesForEntity(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          0,
          10,
        ),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.getLikesForEntity(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          0,
          10,
        ),
      ).rejects.toThrow('Page must be greater than 0');
    });

    it('should throw BadRequestException for invalid limit', async () => {
      // Act & Assert
      await expect(
        requestManager.getLikesForEntity(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          1,
          0,
        ),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.getLikesForEntity(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          1,
          101,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('countLikesForEntity', () => {
    it('should count likes for entity', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = '123e4567-e89b-12d3-a456-426614174001';
      const count = 5;

      provider.countLikesByEntity.mockResolvedValue(count);

      // Act
      const result = await requestManager.countLikesForEntity(
        entityType,
        entityId,
      );

      // Assert
      expect(provider.countLikesByEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
      );
      expect(result).toBe(count);
    });
  });

  describe('getLikesByUser', () => {
    it('should get likes by user with valid pagination', async () => {
      // Arrange
      const userId = '123e4567-e89b-12d3-a456-426614174002';
      const page = 1;
      const limit = 10;
      const likes = [createMockLike()];

      provider.findLikesByUser.mockResolvedValue(likes);

      // Act
      const result = await requestManager.getLikesByUser(userId, page, limit);

      // Assert
      expect(provider.findLikesByUser).toHaveBeenCalledWith(
        userId,
        page,
        limit,
        'createdAt',
        'DESC',
      );
      expect(result).toBe(likes);
    });
  });

  describe('validation methods', () => {
    it('should validate like inputs correctly', () => {
      // Act & Assert - should not throw for valid inputs
      expect(() =>
        (requestManager as any).validateLikeInputs(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          '123e4567-e89b-12d3-a456-426614174002',
        ),
      ).not.toThrow();
    });

    it('should throw for invalid entity type', () => {
      // Act & Assert
      expect(() =>
        (requestManager as any).validateLikeInputs(
          '',
          '123e4567-e89b-12d3-a456-426614174001',
          '123e4567-e89b-12d3-a456-426614174002',
        ),
      ).toThrow('Entity type is required');
    });

    it('should throw for invalid entity ID', () => {
      // Act & Assert
      expect(() =>
        (requestManager as any).validateLikeInputs(
          'video',
          '',
          '123e4567-e89b-12d3-a456-426614174002',
        ),
      ).toThrow('Entity ID is required');
    });

    it('should throw for invalid user ID', () => {
      // Act & Assert
      expect(() =>
        (requestManager as any).validateLikeInputs(
          'video',
          '123e4567-e89b-12d3-a456-426614174001',
          '',
        ),
      ).toThrow('User ID is required');
    });
  });
});
