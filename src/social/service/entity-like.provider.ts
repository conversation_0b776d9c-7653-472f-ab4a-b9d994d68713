import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { Repository, FindManyOptions, FindOneOptions } from 'typeorm';
import { EntityLikeEntity } from '../entity/entity-like.entity';
import { AbstractEntityProvider } from './abstract-entity.provider';

@Injectable()
export class EntityLikeProvider extends AbstractEntityProvider<EntityLikeEntity> {
  constructor(
    @InjectRepository(EntityLikeEntity)
    repository: Repository<EntityLikeEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  /**
   * Validates and filters model IDs to ensure they are valid UUIDs
   * This prevents PostgreSQL UUID validation errors
   */
  private validateModelIds(modelIds: string[]): string[] {
    if (!modelIds || modelIds.length === 0) {
      return [];
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const validModelIds = modelIds.filter(id => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        return false;
      }
      return uuidRegex.test(id.trim());
    });

    if (validModelIds.length !== modelIds.length) {
      this.logger.warn('Some model IDs were filtered out due to invalid UUID format', {
        originalCount: modelIds.length,
        validCount: validModelIds.length,
        invalidIds: modelIds.filter(id => !validModelIds.includes(id)),
      });
    }

    return validModelIds;
  }

  /**
   * Find likes for a specific entity with pagination
   */
  async findLikesByEntity(
    entityType: string,
    entityId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityLikeEntity[]> {
    return this.findByEntity(
      entityType,
      entityId,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }

  /**
   * Count likes for a specific entity
   */
  async countLikesByEntity(
    entityType: string,
    entityId: string,
  ): Promise<number> {
    return this.countByEntity(entityType, entityId);
  }

  /**
   * Find likes by user across all entities
   */
  async findLikesByUser(
    userId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    modelIds?: string[],
  ): Promise<EntityLikeEntity[]> {
    if (!modelIds || modelIds.length === 0) {
      return this.findByUser(userId, page, limit, sortBy, sortOrder);
    }

    // Validate model IDs
    const validModelIds = this.validateModelIds(modelIds);
    if (validModelIds.length === 0) {
      this.logger.warn('No valid model IDs for user likes filtering - returning unfiltered results', {
        userId,
        originalModelIds: modelIds,
      });
      return this.findByUser(userId, page, limit, sortBy, sortOrder);
    }

    // Use custom query with model filtering
    return this.findLikesByUserWithModelFilter(userId, page, limit, sortBy, sortOrder, validModelIds);
  }

  /**
   * Find likes by user with cursor-based pagination
   */
  async findLikesByUserWithCursor(
    userId: string,
    cursor?: string,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    modelIds?: string[],
  ): Promise<EntityLikeEntity[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('like')
      .leftJoinAndSelect('like.user', 'user')
      .where('like.userId = :userId', { userId })
      .andWhere('like.deletedAt IS NULL');

    // Apply cursor filtering
    if (cursor && cursor.trim() !== '') {
      const cursorOperator = sortOrder === 'ASC' ? '>' : '<';

      if (sortBy === 'createdAt') {
        // Parse cursor as timestamp (could be ISO string or Unix timestamp)
        let cursorDate: Date;

        // Try parsing as Unix timestamp first (if it's all digits)
        if (/^\d+$/.test(cursor)) {
          cursorDate = new Date(parseInt(cursor, 10));
        } else {
          // Try parsing as ISO string
          cursorDate = new Date(cursor);
        }

        if (!isNaN(cursorDate.getTime())) {
          queryBuilder.andWhere(`like.${sortBy} ${cursorOperator} :cursor`, {
            cursor: cursorDate,
          });
        }
      } else {
        // For other fields, use direct comparison
        queryBuilder.andWhere(`like.${sortBy} ${cursorOperator} :cursor`, {
          cursor,
        });
      }
    }

    // Apply model filtering if provided
    if (modelIds && modelIds.length > 0) {
      const validModelIds = this.validateModelIds(modelIds);

      if (validModelIds.length > 0) {
        // Add model filtering for both image and video entities
        queryBuilder.andWhere(`(
          (like.entityType = 'image' AND EXISTS (
            SELECT 1 FROM image_completion ic
            INNER JOIN image_completion_model icm ON icm.image_completion_id = ic.id
            WHERE ic.id = like.entityId AND icm.model_id IN (:...validModelIds)
          )) OR
          (like.entityType = 'video' AND EXISTS (
            SELECT 1 FROM video v
            INNER JOIN image_completion oic ON v.original_image_completion_id = oic.id
            INNER JOIN image_completion_model icm2 ON icm2.image_completion_id = oic.id
            WHERE v.id = like.entityId AND icm2.model_id IN (:...validModelIds)
          ))
        )`, { validModelIds });

        this.logger.log('Applied model filtering to user likes cursor query', {
          userId,
          validModelIds,
        });
      }
    }

    // Apply sorting and limit
    queryBuilder.orderBy(`like.${sortBy}`, sortOrder).limit(limit + 1); // Get one extra to check if there are more items

    return queryBuilder.getMany();
  }

  /**
   * Find likes by user with model filtering (offset-based pagination)
   */
  private async findLikesByUserWithModelFilter(
    userId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
    validModelIds: string[],
  ): Promise<EntityLikeEntity[]> {
    const skip = (page - 1) * limit;

    const queryBuilder = this.repository
      .createQueryBuilder('like')
      .leftJoinAndSelect('like.user', 'user')
      .where('like.userId = :userId', { userId })
      .andWhere('like.deletedAt IS NULL');

    // Add model filtering for both image and video entities
    queryBuilder.andWhere(`(
      (like.entityType = 'image' AND EXISTS (
        SELECT 1 FROM image_completion ic
        INNER JOIN image_completion_model icm ON icm.image_completion_id = ic.id
        WHERE ic.id = like.entityId AND icm.model_id IN (:...validModelIds)
      )) OR
      (like.entityType = 'video' AND EXISTS (
        SELECT 1 FROM video v
        INNER JOIN image_completion oic ON v.original_image_completion_id = oic.id
        INNER JOIN image_completion_model icm2 ON icm2.image_completion_id = oic.id
        WHERE v.id = like.entityId AND icm2.model_id IN (:...validModelIds)
      ))
    )`, { validModelIds });

    // Apply sorting, pagination
    queryBuilder
      .orderBy(`like.${sortBy}`, sortOrder)
      .skip(skip)
      .take(limit);

    this.logger.log('Applied model filtering to user likes offset query', {
      userId,
      page,
      limit,
      validModelIds,
    });

    return queryBuilder.getMany();
  }

  /**
   * Find likes by user for a specific entity type
   */
  async findLikesByUserAndEntityType(
    userId: string,
    entityType: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityLikeEntity[]> {
    return this.findBy({ userId, entityType }, page, limit, sortBy, sortOrder);
  }

  /**
   * Check if user has liked a specific entity
   */
  async hasUserLikedEntity(
    entityType: string,
    entityId: string,
    userId: string,
  ): Promise<boolean> {
    return this.exists({ entityType, entityId, userId });
  }

  /**
   * Get user's like for a specific entity (if exists)
   */
  async getUserLikeForEntity(
    entityType: string,
    entityId: string,
    userId: string,
  ): Promise<EntityLikeEntity | null> {
    return this.findOneBy({ entityType, entityId, userId });
  }

  /**
   * Count likes by user across all entities
   */
  async countLikesByUser(userId: string): Promise<number> {
    try {
      return await this.repository.count({
        where: { userId },
      });
    } catch (error) {
      // Handle TypeORM metadata issues in test environment
      if (
        error.message &&
        error.message.includes('No metadata for') &&
        error.message.includes('was found')
      ) {
        this.logger.debug(
          'Using direct SQL count for countLikesByUser due to metadata issue in test environment',
        );
        return await this.countLikesByUserWithDirectSQL(userId);
      }
      throw error;
    }
  }

  /**
   * Find recent likes for an entity type (for feeds, etc.)
   */
  async findRecentLikesByEntityType(
    entityType: string,
    page: number,
    limit: number,
  ): Promise<EntityLikeEntity[]> {
    return this.findBy({ entityType }, page, limit, 'createdAt', 'DESC');
  }

  /**
   * Get like statistics for multiple entities
   */
  async getLikeStatsForEntities(
    entityType: string,
    entityIds: string[],
  ): Promise<{ entityId: string; likeCount: number }[]> {
    if (entityIds.length === 0) {
      return [];
    }

    const results = await this.repository
      .createQueryBuilder('entityLike')
      .select('entityLike.entityId', 'entityId')
      .addSelect('COUNT(*)', 'likeCount')
      .where('entityLike.entityType = :entityType', { entityType })
      .andWhere('entityLike.entityId IN (:...entityIds)', { entityIds })
      .andWhere('entityLike.deletedAt IS NULL')
      .groupBy('entityLike.entityId')
      .getRawMany();

    // Ensure all entities are represented, even with 0 likes
    const statsMap = new Map(
      results.map((r) => [r.entityId, parseInt(r.likeCount, 10)]),
    );

    return entityIds.map((entityId) => ({
      entityId,
      likeCount: statsMap.get(entityId) || 0,
    }));
  }

  /**
   * Get top liked entities for a specific entity type
   */
  async getTopLikedEntities(
    entityType: string,
    limit: number,
    timeframe?: 'day' | 'week' | 'month' | 'year',
  ): Promise<{ entityId: string; likeCount: number }[]> {
    try {
      let query = this.repository
        .createQueryBuilder('entityLike')
        .select('entityLike.entityId', 'entityId')
        .addSelect('COUNT(*)', 'likeCount')
        .where('entityLike.entityType = :entityType', { entityType })
        .andWhere('entityLike.deletedAt IS NULL');

      // Add timeframe filter if specified
      if (timeframe) {
        const timeframeMap = {
          day: '1 day',
          week: '1 week',
          month: '1 month',
          year: '1 year',
        };

        query = query.andWhere(
          'entityLike.createdAt >= NOW() - INTERVAL :timeframe',
          { timeframe: timeframeMap[timeframe] },
        );
      }

      const results = await query
        .groupBy('entityLike.entityId')
        .orderBy('likeCount', 'DESC')
        .limit(limit)
        .getRawMany();

      return results.map((r) => ({
        entityId: r.entityId,
        likeCount: parseInt(r.likeCount, 10),
      }));
    } catch (error) {
      // Handle TypeORM metadata issues in test environment
      if (
        error.message &&
        error.message.includes('No metadata for') &&
        error.message.includes('was found')
      ) {
        this.logger.debug(
          'Using direct SQL for getTopLikedEntities due to metadata issue in test environment',
        );
        return await this.getTopLikedEntitiesWithDirectSQL(
          entityType,
          limit,
          timeframe,
        );
      }
      throw error;
    }
  }

  /**
   * Get user likes ranking across all entities
   */
  async findUserLikesRanking(
    start: string,
    end: string,
    page: number,
    limit: number,
    userId: string = null,
  ): Promise<any[]> {
    try {
      let query = this.prepareUserLikesRankingQuery(start, end, userId);

      query += `
        ORDER BY COUNT(entity_like.id) DESC
        LIMIT ${limit}
        OFFSET ${(page - 1) * limit};
      `;

      return await this.repository.query(query);
    } catch (error) {
      // Handle TypeORM metadata issues in test environment
      if (
        error.message &&
        error.message.includes('No metadata for') &&
        error.message.includes('was found')
      ) {
        this.logger.debug(
          'Using direct SQL for findUserLikesRanking due to metadata issue in test environment',
        );
        return await this.findUserLikesRankingWithDirectSQL(
          start,
          end,
          page,
          limit,
          userId,
        );
      }
      throw error;
    }
  }

  /**
   * Count user likes ranking across all entities
   */
  async countUserLikesRanking(
    start: string,
    end: string,
    userId: string = null,
  ): Promise<number> {
    try {
      const query =
        `SELECT COUNT(*) AS totalCount FROM ( ` +
        this.prepareUserLikesRankingQuery(start, end, userId) +
        `) x`;

      const result = await this.repository.query(query);
      return parseInt(result[0]?.totalCount || '0', 10);
    } catch (error) {
      // Handle TypeORM metadata issues in test environment
      if (
        error.message &&
        error.message.includes('No metadata for') &&
        error.message.includes('was found')
      ) {
        this.logger.debug(
          'Using direct SQL for countUserLikesRanking due to metadata issue in test environment',
        );
        return await this.countUserLikesRankingWithDirectSQL(
          start,
          end,
          userId,
        );
      }
      throw error;
    }
  }

  /**
   * Override prepareFindOneOptions to include user relation
   */
  prepareFindOneOptions(criteria: any): FindOneOptions<EntityLikeEntity> {
    return {
      where: criteria,
      relations: this.getDefaultRelations(),
    };
  }

  /**
   * Get default relations for like queries
   */
  protected getDefaultRelations(): string[] {
    return ['user'];
  }

  /**
   * Fallback method to count likes by user using direct SQL when TypeORM metadata is not available
   */
  private async countLikesByUserWithDirectSQL(userId: string): Promise<number> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM entity_like
        WHERE user_id = $1
          AND deleted_at IS NULL
      `;

      this.logger.debug(
        'Executing direct SQL count query for countLikesByUser',
        { query, userId },
      );

      const results = await this.repository.manager.query(query, [userId]);

      return parseInt(results[0]?.count || '0', 10);
    } catch (error) {
      this.logger.error('Direct SQL count query for countLikesByUser failed', {
        error: error.message,
        userId,
      });

      return 0;
    }
  }

  /**
   * Prepare user likes ranking query
   */
  private prepareUserLikesRankingQuery(
    start: string,
    end: string,
    userId: string = null,
  ): string {
    let userFilter = '';

    if (userId) {
      userFilter = `AND entity_like.user_id = '${userId}'`;
    }

    return `
      SELECT entity_like.user_id AS user_id,
      COUNT(entity_like.id) AS likes_count,
      RANK() OVER (ORDER BY COUNT(*) DESC) AS rank
      FROM entity_like
      WHERE entity_like.created_at BETWEEN '${start}' AND '${end}'
      AND entity_like.deleted_at IS NULL
      ${userFilter}
      GROUP BY entity_like.user_id
    `;
  }

  /**
   * Fallback method for user likes ranking using direct SQL
   */
  private async findUserLikesRankingWithDirectSQL(
    start: string,
    end: string,
    page: number,
    limit: number,
    userId: string = null,
  ): Promise<any[]> {
    try {
      let userFilter = '';
      const parameters = [start, end];

      if (userId) {
        userFilter = `AND user_id = $3`;
        parameters.push(userId);
      }

      const query = `
        SELECT user_id, COUNT(*) as likes_count,
        RANK() OVER (ORDER BY COUNT(*) DESC) AS rank
        FROM entity_like
        WHERE created_at BETWEEN $1 AND $2
          AND deleted_at IS NULL
          ${userFilter}
        GROUP BY user_id
        ORDER BY likes_count DESC
        LIMIT ${limit}
        OFFSET ${(page - 1) * limit}
      `;

      this.logger.debug('Executing direct SQL query for findUserLikesRanking', {
        query,
        parameters,
      });

      return await this.repository.manager.query(query, parameters);
    } catch (error) {
      this.logger.error('Direct SQL query for findUserLikesRanking failed', {
        error: error.message,
        start,
        end,
        page,
        limit,
        userId,
      });

      return [];
    }
  }

  /**
   * Fallback method for counting user likes ranking using direct SQL
   */
  private async countUserLikesRankingWithDirectSQL(
    start: string,
    end: string,
    userId: string = null,
  ): Promise<number> {
    try {
      let userFilter = '';
      const parameters = [start, end];

      if (userId) {
        userFilter = `AND user_id = $3`;
        parameters.push(userId);
      }

      const query = `
        SELECT COUNT(*) as totalCount FROM (
          SELECT user_id
          FROM entity_like
          WHERE created_at BETWEEN $1 AND $2
            AND deleted_at IS NULL
            ${userFilter}
          GROUP BY user_id
        ) x
      `;

      this.logger.debug(
        'Executing direct SQL count query for countUserLikesRanking',
        { query, parameters },
      );

      const results = await this.repository.manager.query(query, parameters);
      return parseInt(results[0]?.totalcount || '0', 10);
    } catch (error) {
      this.logger.error(
        'Direct SQL count query for countUserLikesRanking failed',
        {
          error: error.message,
          start,
          end,
          userId,
        },
      );

      return 0;
    }
  }

  /**
   * Fallback method to get top liked entities using direct SQL when TypeORM metadata is not available
   */
  private async getTopLikedEntitiesWithDirectSQL(
    entityType: string,
    limit: number,
    timeframe?: 'day' | 'week' | 'month' | 'year',
  ): Promise<{ entityId: string; likeCount: number }[]> {
    try {
      let timeframeCondition = '';
      const parameters = [entityType, limit];

      if (timeframe) {
        const timeframeMap = {
          day: '1 day',
          week: '1 week',
          month: '1 month',
          year: '1 year',
        };

        timeframeCondition = `AND created_at >= NOW() - INTERVAL '${timeframeMap[timeframe]}'`;
      }

      const query = `
        SELECT entity_id as "entityId", COUNT(*) as "likeCount"
        FROM entity_like
        WHERE entity_type = $1
          AND deleted_at IS NULL
          ${timeframeCondition}
        GROUP BY entity_id
        ORDER BY "likeCount" DESC
        LIMIT $2
      `;

      this.logger.debug('Executing direct SQL query for getTopLikedEntities', {
        query,
        parameters,
      });

      const results = await this.repository.manager.query(query, parameters);

      return results.map((row: any) => ({
        entityId: row.entityId,
        likeCount: parseInt(row.likeCount, 10),
      }));
    } catch (error) {
      this.logger.error('Direct SQL query for getTopLikedEntities failed', {
        error: error.message,
        entityType,
        limit,
        timeframe,
      });

      return [];
    }
  }
}
