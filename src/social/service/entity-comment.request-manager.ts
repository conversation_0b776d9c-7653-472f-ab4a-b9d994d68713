import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  NotFoundException,
} from '@nestjs/common';
import { EntityCommentManager } from './entity-comment.manager';
import { EntityCommentProvider } from './entity-comment.provider';
import { EntityCommentRequest } from '../dto/entity-comment.request';
import { EntityCommentEntity } from '../entity/entity-comment.entity';
import { EntityValidator } from './entity-validator.interface';

@Injectable()
export class EntityCommentRequestManager {
  constructor(
    private manager: EntityCommentManager,
    private provider: EntityCommentProvider,
  ) {}

  /**
   * Create a comment on an entity
   */
  async createComment(
    entityType: string,
    entityId: string,
    userId: string,
    request: EntityCommentRequest,
    entityValidator: EntityValidator,
  ): Promise<EntityCommentEntity> {
    // Validate request
    this.validateCommentRequest(request);

    // Check if entity exists and user can comment
    await this.validateEntityForComment(entityType, entityId, entityValidator);

    // Create the comment
    return this.manager.createComment(
      entityType,
      entityId,
      userId,
      request.comment,
      entityValidator,
    );
  }

  /**
   * Delete a comment
   */
  async deleteComment(
    commentId: string,
    userId: string,
    entityValidator: EntityValidator,
  ): Promise<void> {
    // Get the comment
    const comment = await this.provider.get(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Check if user can delete this comment
    await this.validateCommentDeletion(comment, userId);

    // Delete the comment
    await this.manager.deleteComment(comment, entityValidator);
  }

  /**
   * Get comments for an entity with pagination
   */
  async getCommentsForEntity(
    entityType: string,
    entityId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityCommentEntity[]> {
    // Validate pagination parameters
    this.validatePaginationParams(page, limit);

    return this.provider.findCommentsByEntity(
      entityType,
      entityId,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }

  /**
   * Count comments for an entity
   */
  async countCommentsForEntity(
    entityType: string,
    entityId: string,
  ): Promise<number> {
    return this.provider.countCommentsByEntity(entityType, entityId);
  }

  /**
   * Get comments by user
   */
  async getCommentsByUser(
    userId: string,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<EntityCommentEntity[]> {
    this.validatePaginationParams(page, limit);

    return this.provider.findCommentsByUser(
      userId,
      page,
      limit,
      sortBy,
      sortOrder,
    );
  }

  /**
   * Validate comment request
   */
  private validateCommentRequest(request: EntityCommentRequest): void {
    if (!request.comment || request.comment.trim().length === 0) {
      throw new BadRequestException('Comment text is required');
    }

    if (request.comment.length > 2000) {
      throw new BadRequestException(
        'Comment text is too long (max 2000 characters)',
      );
    }

    // Check for spam patterns (basic)
    const trimmedComment = request.comment.trim();
    if (trimmedComment.length < 2) {
      throw new BadRequestException('Comment is too short');
    }

    // Check for excessive repetition
    if (this.isSpamComment(trimmedComment)) {
      throw new BadRequestException('Comment appears to be spam');
    }
  }

  /**
   * Validate entity for commenting
   */
  private async validateEntityForComment(
    entityType: string,
    entityId: string,
    entityValidator: EntityValidator,
  ): Promise<void> {
    try {
      await entityValidator.validateExists(entityId);
    } catch (error) {
      // Preserve the original exception type for proper HTTP status codes
      if (error.name === 'NotFoundException') {
        throw error; // Keep as 404 Not Found
      } else if (error.name === 'ForbiddenException') {
        throw error; // Keep as 403 Forbidden
      } else {
        // Convert other errors to BadRequestException
        throw new BadRequestException(
          `Cannot comment on this ${entityType}: ${error.message}`,
        );
      }
    }
  }

  /**
   * Validate comment deletion permissions
   */
  private async validateCommentDeletion(
    comment: EntityCommentEntity,
    userId: string,
  ): Promise<void> {
    // Users can only delete their own comments
    if (comment.userId !== userId) {
      throw new ForbiddenException('You can only delete your own comments');
    }
  }

  /**
   * Validate pagination parameters
   */
  private validatePaginationParams(page: number, limit: number): void {
    if (page < 1) {
      throw new BadRequestException('Page must be greater than 0');
    }

    if (limit < 1 || limit > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }
  }

  /**
   * Basic spam detection
   */
  private isSpamComment(comment: string): boolean {
    // Check for excessive repetition of characters
    const repeatedCharPattern = /(.)\1{10,}/;
    if (repeatedCharPattern.test(comment)) {
      return true;
    }

    // Check for excessive repetition of words
    const words = comment.toLowerCase().split(/\s+/);
    const wordCount = new Map<string, number>();

    for (const word of words) {
      if (word.length > 2) {
        wordCount.set(word, (wordCount.get(word) || 0) + 1);
        if (wordCount.get(word)! > Math.max(3, words.length / 3)) {
          return true;
        }
      }
    }

    return false;
  }
}
