import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { VideoEntityValidator } from './video-entity.validator';
import { VideoProvider } from '../../video/service/provider';
import {
  VideoEntity,
  StatusEnum,
  PrivacyEnum,
} from '../../video/entity/video.entity';
import { Logger } from 'nestjs-pino';

describe('VideoEntityValidator', () => {
  let validator: VideoEntityValidator;
  let videoProvider: jest.Mocked<VideoProvider>;
  let logger: jest.Mocked<Logger>;

  const createMockVideo = (overrides: Partial<VideoEntity> = {}): any => ({
    id: 'video-1',
    userId: 'user-1',
    status: StatusEnum.READY,
    privacy: PrivacyEnum.PUBLIC,
    createdAt: new Date(),
    deletedAt: null,
    prompt: 'Test video',
    likes: 10,
    comments: 5,
    isNsfw: false,
    hidePrompt: false,
    inputImageUrl: 'https://example.com/image.jpg',
    width: 1024,
    height: 768,
    originalImageCompletion: {
      id: 'image-1',
      imagePaths: {
        thumbnail: 'https://example.com/thumb.jpg',
        original: 'https://example.com/original.jpg',
      },
    },
    ...overrides,
  });

  const mockVideo = createMockVideo();

  beforeEach(async () => {
    const mockVideoProvider = {
      findOne: jest.fn(),
      findByIdWithRelations: jest.fn(),
    };

    const mockLogger = {
      debug: jest.fn(),
      error: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VideoEntityValidator,
        {
          provide: VideoProvider,
          useValue: mockVideoProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    validator = module.get<VideoEntityValidator>(VideoEntityValidator);
    videoProvider = module.get(VideoProvider);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateExists', () => {
    it('should validate successfully for a ready public video', async () => {
      // Arrange
      const entityId = 'video-1';
      videoProvider.findOne.mockResolvedValue(mockVideo);

      // Act
      await validator.validateExists(entityId);

      // Assert
      expect(videoProvider.findOne).toHaveBeenCalledWith(entityId);
      expect(logger.debug).toHaveBeenCalledWith(
        'VideoEntityValidator: Validating video existence',
        { entityId },
      );
    });

    it('should validate successfully for a saved video', async () => {
      // Arrange
      const entityId = 'video-1';
      const savedVideo = createMockVideo({ status: StatusEnum.SAVED });
      videoProvider.findOne.mockResolvedValue(savedVideo);

      // Act
      await validator.validateExists(entityId);

      // Assert
      expect(videoProvider.findOne).toHaveBeenCalledWith(entityId);
    });

    it('should throw NotFoundException when video does not exist', async () => {
      // Arrange
      const entityId = 'nonexistent-video';
      videoProvider.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Video not found',
      );
      expect(videoProvider.findOne).toHaveBeenCalledWith(entityId);
    });

    it('should throw NotFoundException when video is soft deleted', async () => {
      // Arrange
      const entityId = 'video-1';
      const deletedVideo = createMockVideo({ deletedAt: new Date() });
      videoProvider.findOne.mockResolvedValue(deletedVideo);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Video has been deleted',
      );
    });

    it('should throw ForbiddenException when video is generating', async () => {
      // Arrange
      const entityId = 'video-1';
      const generatingVideo = createMockVideo({
        status: StatusEnum.GENERATING,
      });
      videoProvider.findOne.mockResolvedValue(generatingVideo);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Video is not ready for interaction',
      );
    });

    it('should throw ForbiddenException when video has failed', async () => {
      // Arrange
      const entityId = 'video-1';
      const failedVideo = createMockVideo({ status: StatusEnum.FAILED });
      videoProvider.findOne.mockResolvedValue(failedVideo);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Video is not ready for interaction',
      );
    });

    it('should log debug message for private video but not throw error', async () => {
      // Arrange
      const entityId = 'video-1';
      const privateVideo = createMockVideo({ privacy: PrivacyEnum.PRIVATE });
      videoProvider.findOne.mockResolvedValue(privateVideo);

      // Act
      await validator.validateExists(entityId);

      // Assert
      expect(logger.debug).toHaveBeenCalledWith(
        'Video is private, user authorization required',
        { videoId: entityId, privacy: PrivacyEnum.PRIVATE },
      );
    });
  });

  describe('getEntityInfo', () => {
    it('should return complete entity info for a valid video', async () => {
      // Arrange
      const entityId = 'video-1';
      videoProvider.findByIdWithRelations.mockResolvedValue(mockVideo);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result).toEqual({
        id: mockVideo.id,
        ownerId: mockVideo.userId,
        title: mockVideo.prompt,
        thumbnail: mockVideo.originalImageCompletion.imagePaths.thumbnail,
        status: mockVideo.status,
        privacy: mockVideo.privacy,
        createdAt: mockVideo.createdAt,
        likes: mockVideo.likes,
        comments: mockVideo.comments,
        isNsfw: mockVideo.isNsfw,
        hidePrompt: mockVideo.hidePrompt,
      });
      expect(videoProvider.findByIdWithRelations).toHaveBeenCalledWith(
        entityId,
        ['user', 'originalImageCompletion'],
      );
    });

    it('should return prompt as title even when hidePrompt is true', async () => {
      // Arrange
      const entityId = 'video-1';
      const hiddenPromptVideo = createMockVideo({ hidePrompt: true });
      videoProvider.findByIdWithRelations.mockResolvedValue(hiddenPromptVideo);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result.title).toBe('Test video'); // The validator doesn't check hidePrompt for title
      expect(result.hidePrompt).toBe(true);
    });

    it('should handle missing original image completion gracefully', async () => {
      // Arrange
      const entityId = 'video-1';
      const videoWithoutImage = createMockVideo({
        originalImageCompletion: null,
      });
      videoProvider.findByIdWithRelations.mockResolvedValue(videoWithoutImage);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result.thumbnail).toBeNull();
    });

    it('should throw NotFoundException when video does not exist', async () => {
      // Arrange
      const entityId = 'nonexistent-video';
      videoProvider.findByIdWithRelations.mockResolvedValue(null);

      // Act & Assert
      await expect(validator.getEntityInfo(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.getEntityInfo(entityId)).rejects.toThrow(
        'Video not found',
      );
    });

    it('should handle video with input image URL but no thumbnail', async () => {
      // Arrange
      const entityId = 'video-1';
      const videoWithInputUrl = createMockVideo({
        originalImageCompletion: null,
        inputImageUrl: 'https://example.com/input.jpg',
      });
      videoProvider.findByIdWithRelations.mockResolvedValue(videoWithInputUrl);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result.thumbnail).toBeNull(); // The validator doesn't use inputImageUrl as fallback
    });
  });
});
