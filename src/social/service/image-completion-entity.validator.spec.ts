import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { ImageCompletionEntityValidator } from './image-completion-entity.validator';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import {
  ImageCompletionEntity,
  StatusEnum,
  PrivacyEnum,
} from '../../image-completion/entity/image-completion.entity';
import { Logger } from 'nestjs-pino';

describe('ImageCompletionEntityValidator', () => {
  let validator: ImageCompletionEntityValidator;
  let imageCompletionProvider: jest.Mocked<ImageCompletionProvider>;
  let logger: jest.Mocked<Logger>;

  const createMockImage = (
    overrides: Partial<ImageCompletionEntity> = {},
  ): any => ({
    id: 'image-1',
    userId: 'user-1',
    status: StatusEnum.READY,
    privacy: PrivacyEnum.PUBLIC,
    createdAt: new Date(),
    deletedAt: null,
    prompt: 'Test image',
    likes: 10,
    comments: 5,
    isNsfw: false,
    isUnsafe: false,
    hidePrompt: false,
    imagePaths: {
      thumbnail: 'https://example.com/thumb.jpg',
      original: 'https://example.com/original.jpg',
    },
    hasWatermark: true,
    generationSettings: { width: 512, height: 512 },
    models: [],
    hasModel: jest.fn().mockReturnValue(false),
    ...overrides,
  });

  const mockImage = createMockImage();

  beforeEach(async () => {
    const mockImageCompletionProvider = {
      findOne: jest.fn(),
    };

    const mockLogger = {
      debug: jest.fn(),
      error: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageCompletionEntityValidator,
        {
          provide: ImageCompletionProvider,
          useValue: mockImageCompletionProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    validator = module.get<ImageCompletionEntityValidator>(
      ImageCompletionEntityValidator,
    );
    imageCompletionProvider = module.get(ImageCompletionProvider);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateExists', () => {
    it('should validate successfully for a ready public image', async () => {
      // Arrange
      const entityId = 'image-1';
      imageCompletionProvider.findOne.mockResolvedValue(mockImage);

      // Act
      await validator.validateExists(entityId);

      // Assert
      expect(imageCompletionProvider.findOne).toHaveBeenCalledWith(entityId);
      expect(logger.debug).toHaveBeenCalledWith(
        'ImageCompletionEntityValidator: Validating image existence',
        { entityId },
      );
      expect(logger.debug).toHaveBeenCalledWith(
        'ImageCompletionEntityValidator: Image validation successful',
        {
          entityId,
          status: mockImage.status,
          privacy: mockImage.privacy,
        },
      );
    });

    it('should throw NotFoundException when image does not exist', async () => {
      // Arrange
      const entityId = 'nonexistent-image';
      imageCompletionProvider.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Image not found',
      );
      expect(imageCompletionProvider.findOne).toHaveBeenCalledWith(entityId);
    });

    it('should throw NotFoundException when image is soft deleted', async () => {
      // Arrange
      const entityId = 'image-1';
      const deletedImage = createMockImage({ deletedAt: new Date() });
      imageCompletionProvider.findOne.mockResolvedValue(deletedImage);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Image has been deleted',
      );
    });

    it('should throw ForbiddenException when image is not ready', async () => {
      // Arrange
      const entityId = 'image-1';
      const generatingImage = createMockImage({
        status: StatusEnum.GENERATING,
      });
      imageCompletionProvider.findOne.mockResolvedValue(generatingImage);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Image is not ready for interaction',
      );
    });

    it('should throw ForbiddenException when image is private', async () => {
      // Arrange
      const entityId = 'image-1';
      const privateImage = createMockImage({ privacy: PrivacyEnum.PRIVATE });
      imageCompletionProvider.findOne.mockResolvedValue(privateImage);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Cannot interact with private images',
      );
    });

    it('should throw ForbiddenException when image is unsafe', async () => {
      // Arrange
      const entityId = 'image-1';
      const unsafeImage = createMockImage({ isUnsafe: true });
      imageCompletionProvider.findOne.mockResolvedValue(unsafeImage);

      // Act & Assert
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(validator.validateExists(entityId)).rejects.toThrow(
        'Cannot interact with unsafe images',
      );
    });
  });

  describe('getEntityInfo', () => {
    it('should return complete entity info for a valid image', async () => {
      // Arrange
      const entityId = 'image-1';
      imageCompletionProvider.findOne.mockResolvedValue(mockImage);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result).toEqual({
        id: mockImage.id,
        ownerId: mockImage.userId,
        title: mockImage.prompt,
        thumbnail: mockImage.imagePaths.thumbnail,
        status: mockImage.status,
        privacy: mockImage.privacy,
        createdAt: mockImage.createdAt,
        likes: mockImage.likes,
        comments: mockImage.comments,
        isNsfw: mockImage.isNsfw,
        hidePrompt: mockImage.hidePrompt,
        prompt: mockImage.prompt,
        imagePaths: mockImage.imagePaths,
        hasWatermark: mockImage.hasWatermark,
        generationSettings: mockImage.generationSettings,
      });
      expect(imageCompletionProvider.findOne).toHaveBeenCalledWith(entityId);
    });

    it('should return default title when prompt is hidden', async () => {
      // Arrange
      const entityId = 'image-1';
      const hiddenPromptImage = createMockImage({ hidePrompt: true });
      imageCompletionProvider.findOne.mockResolvedValue(hiddenPromptImage);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result.title).toBe('Generated Image');
      expect(result.prompt).toBeNull();
      expect(result.hidePrompt).toBe(true);
    });

    it('should handle missing image paths gracefully', async () => {
      // Arrange
      const entityId = 'image-1';
      const imageWithoutPaths = createMockImage({ imagePaths: null });
      imageCompletionProvider.findOne.mockResolvedValue(imageWithoutPaths);

      // Act
      const result = await validator.getEntityInfo(entityId);

      // Assert
      expect(result.thumbnail).toBeNull();
      expect(result.imagePaths).toBeNull();
    });

    it('should throw NotFoundException when image does not exist', async () => {
      // Arrange
      const entityId = 'nonexistent-image';
      imageCompletionProvider.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(validator.getEntityInfo(entityId)).rejects.toThrow(
        NotFoundException,
      );
      await expect(validator.getEntityInfo(entityId)).rejects.toThrow(
        'Image not found',
      );
    });
  });
});
