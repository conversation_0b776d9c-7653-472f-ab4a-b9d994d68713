import { Test, TestingModule } from '@nestjs/testing';
import { EntityCommentNotificationService } from './entity-comment.notification';
import { EntityValidatorService } from '../service/entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { Logger } from 'nestjs-pino';

describe('EntityCommentNotificationService', () => {
  let service: EntityCommentNotificationService;
  let entityValidatorService: jest.Mocked<EntityValidatorService>;
  let userProvider: jest.Mocked<UserProvider>;
  let logger: jest.Mocked<Logger>;

  const mockEntityInfo = {
    id: 'video-123',
    ownerId: 'owner-456',
    title: 'Amazing Video',
    thumbnail: 'https://example.com/thumb.jpg',
    status: 'ready',
    privacy: 'public',
    createdAt: new Date(),
    likes: 10,
    comments: 5,
    isNsfw: false,
    hidePrompt: false,
  };

  const mockUser = {
    id: 'user-789',
    username: 'commenter_user',
    email: '<EMAIL>',
    includeWatermarks: false,
    hidePrompt: false,
    isActive: true,
    isVerified: false,
    allowSwapping: false,
    isBot: false,
    followers: [],
    following: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  };

  beforeEach(async () => {
    const mockEntityValidatorService = {
      getEntityInfo: jest.fn(),
    };

    const mockUserProvider = {
      get: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityCommentNotificationService,
        {
          provide: EntityValidatorService,
          useValue: mockEntityValidatorService,
        },
        {
          provide: UserProvider,
          useValue: mockUserProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<EntityCommentNotificationService>(
      EntityCommentNotificationService,
    );
    entityValidatorService = module.get(EntityValidatorService);
    userProvider = module.get(UserProvider);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleCommentCreated', () => {
    const commentPayload = {
      commentId: 'comment-123',
      entityType: 'video',
      entityId: 'video-123',
      userId: 'user-789',
      comment: 'Great video!',
    };

    it('should handle comment created notification successfully', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(mockUser);

      // Act
      await service.handleCommentCreated(commentPayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(userProvider.get).toHaveBeenCalledWith('user-789');
      expect(logger.log).toHaveBeenCalledWith('Comment created notification', {
        recipientId: 'owner-456',
        message: 'commenter_user commented on Amazing Video',
        data: expect.objectContaining({
          commentId: 'comment-123',
          entityId: 'video-123',
          entityOwnerId: 'owner-456',
          entityType: 'video',
          userId: 'user-789',
          comment: 'Great video!',
          entityTitle: 'Amazing Video',
          entityThumbnail: 'https://example.com/thumb.jpg',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should not notify when user comments on their own entity', async () => {
      // Arrange
      const ownCommentPayload = { ...commentPayload, userId: 'owner-456' };
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);

      // Act
      await service.handleCommentCreated(ownCommentPayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(userProvider.get).not.toHaveBeenCalled();
      expect(logger.log).not.toHaveBeenCalled();
    });

    it('should warn when commenter user not found', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(null);

      // Act
      await service.handleCommentCreated(commentPayload);

      // Assert
      expect(logger.warn).toHaveBeenCalledWith(
        'Commenter not found for notification',
        {
          userId: 'user-789',
          commentId: 'comment-123',
        },
      );
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const error = new Error('Database error');
      entityValidatorService.getEntityInfo.mockRejectedValue(error);

      // Act
      await service.handleCommentCreated(commentPayload);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to handle comment created notification',
        {
          error: 'Database error',
          payload: {
            commentId: 'comment-123',
            entityType: 'video',
            entityId: 'video-123',
            userId: 'user-789',
            comment: 'Great video!',
          },
        },
      );
    });
  });

  describe('handleCommentLiked', () => {
    const likePayload = {
      commentLikeId: 'like-123',
      commentId: 'comment-123',
      entityType: 'video',
      entityId: 'video-123',
      userId: 'user-789',
      commentOwnerId: 'comment-owner-456',
    };

    it('should handle comment liked notification successfully', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(mockUser);

      // Act
      await service.handleCommentLiked(likePayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(userProvider.get).toHaveBeenCalledWith('user-789');
      expect(logger.log).toHaveBeenCalledWith('Comment liked notification', {
        recipientId: 'comment-owner-456',
        message: 'commenter_user liked your comment on Amazing Video',
        data: expect.objectContaining({
          commentId: 'comment-123',
          commentLikeId: 'like-123',
          commentOwnerId: 'comment-owner-456',
          entityId: 'video-123',
          entityType: 'video',
          userId: 'user-789',
          entityTitle: 'Amazing Video',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should not notify when user likes their own comment', async () => {
      // Arrange
      const ownLikePayload = { ...likePayload, userId: 'comment-owner-456' };
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);

      // Act
      await service.handleCommentLiked(ownLikePayload);

      // Assert - Should return early without calling any services
      expect(entityValidatorService.getEntityInfo).not.toHaveBeenCalled();
      expect(userProvider.get).not.toHaveBeenCalled();
      expect(logger.log).not.toHaveBeenCalled();
    });

    it('should warn when liker user not found', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(null);

      // Act
      await service.handleCommentLiked(likePayload);

      // Assert
      expect(logger.warn).toHaveBeenCalledWith(
        'Liker not found for notification',
        {
          userId: 'user-789',
          commentId: 'comment-123',
        },
      );
    });
  });

  describe('message generation', () => {
    it('should generate comment created message for video', () => {
      // Act
      const message = (service as any).generateCommentCreatedMessage(
        'john_doe',
        'video',
        'Amazing Video',
      );

      // Assert
      expect(message).toBe('john_doe commented on Amazing Video');
    });

    it('should generate comment created message for image', () => {
      // Act
      const message = (service as any).generateCommentCreatedMessage(
        'jane_doe',
        'image',
        'Beautiful Art',
      );

      // Assert
      expect(message).toBe('jane_doe commented on Beautiful Art');
    });

    it('should generate comment created message with default title', () => {
      // Act
      const message = (service as any).generateCommentCreatedMessage(
        'user123',
        'video',
        null,
      );

      // Assert
      expect(message).toBe('user123 commented on your video');
    });

    it('should generate comment liked message', () => {
      // Act
      const message = (service as any).generateCommentLikedMessage(
        'alice',
        'video',
        'Great Video',
      );

      // Assert
      expect(message).toBe('alice liked your comment on Great Video');
    });

    it('should generate comment liked message with default title', () => {
      // Act
      const message = (service as any).generateCommentLikedMessage(
        'bob',
        'image',
        null,
      );

      // Assert
      expect(message).toBe('bob liked your comment on a image');
    });
  });
});
