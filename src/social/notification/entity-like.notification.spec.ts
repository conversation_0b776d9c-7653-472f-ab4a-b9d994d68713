import { Test, TestingModule } from '@nestjs/testing';
import { EntityLikeNotificationService } from './entity-like.notification';
import { EntityValidatorService } from '../service/entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { Logger } from 'nestjs-pino';

describe('EntityLikeNotificationService', () => {
  let service: EntityLikeNotificationService;
  let entityValidatorService: jest.Mocked<EntityValidatorService>;
  let userProvider: jest.Mocked<UserProvider>;
  let logger: jest.Mocked<Logger>;

  const mockEntityInfo = {
    id: 'video-123',
    ownerId: 'owner-456',
    title: 'Amazing Video',
    thumbnail: 'https://example.com/thumb.jpg',
    status: 'ready',
    privacy: 'public',
    createdAt: new Date(),
    likes: 10,
    comments: 5,
    isNsfw: false,
    hidePrompt: false,
  };

  const mockUser = {
    id: 'user-789',
    username: 'liker_user',
    email: '<EMAIL>',
    includeWatermarks: false,
    hidePrompt: false,
    isActive: true,
    isVerified: false,
    allowSwapping: false,
    isBot: false,
    followers: [],
    following: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  };

  beforeEach(async () => {
    const mockEntityValidatorService = {
      getEntityInfo: jest.fn(),
    };

    const mockUserProvider = {
      get: jest.fn(),
    };

    const mockLogger = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityLikeNotificationService,
        {
          provide: EntityValidatorService,
          useValue: mockEntityValidatorService,
        },
        {
          provide: UserProvider,
          useValue: mockUserProvider,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<EntityLikeNotificationService>(
      EntityLikeNotificationService,
    );
    entityValidatorService = module.get(EntityValidatorService);
    userProvider = module.get(UserProvider);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleEntityLiked', () => {
    const likePayload = {
      likeId: 'like-123',
      entityType: 'video',
      entityId: 'video-123',
      userId: 'user-789',
    };

    it('should handle entity liked notification successfully', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(mockUser);

      // Act
      await service.handleEntityLiked(likePayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(userProvider.get).toHaveBeenCalledWith('user-789');
      expect(logger.log).toHaveBeenCalledWith('Entity liked notification', {
        recipientId: 'owner-456',
        message: 'liker_user liked Amazing Video',
        data: expect.objectContaining({
          likeId: 'like-123',
          entityType: 'video',
          entityId: 'video-123',
          entityOwnerId: 'owner-456',
          userId: 'user-789',
          entityTitle: 'Amazing Video',
          entityThumbnail: 'https://example.com/thumb.jpg',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should not notify when user likes their own entity', async () => {
      // Arrange
      const ownLikePayload = { ...likePayload, userId: 'owner-456' };
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);

      // Act
      await service.handleEntityLiked(ownLikePayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(userProvider.get).not.toHaveBeenCalled();
      expect(logger.log).not.toHaveBeenCalled();
    });

    it('should warn when liker user not found', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);
      userProvider.get.mockResolvedValue(null);

      // Act
      await service.handleEntityLiked(likePayload);

      // Assert
      expect(logger.warn).toHaveBeenCalledWith(
        'Liker not found for notification',
        {
          userId: 'user-789',
          likeId: 'like-123',
        },
      );
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const error = new Error('Database error');
      entityValidatorService.getEntityInfo.mockRejectedValue(error);

      // Act
      await service.handleEntityLiked(likePayload);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to handle entity liked notification',
        {
          error: 'Database error',
          payload: {
            likeId: 'like-123',
            entityType: 'video',
            entityId: 'video-123',
            userId: 'user-789',
          },
        },
      );
    });
  });

  describe('handleLikeMilestone', () => {
    const milestonePayload = {
      likeId: 'like-123',
      entityType: 'video',
      entityId: 'video-123',
      userId: 'user-789',
      milestone: 100,
      totalLikes: 100,
    };

    it('should handle like milestone notification successfully', async () => {
      // Arrange
      entityValidatorService.getEntityInfo.mockResolvedValue(mockEntityInfo);

      // Act
      await service.handleLikeMilestone(milestonePayload);

      // Assert
      expect(entityValidatorService.getEntityInfo).toHaveBeenCalledWith(
        'video',
        'video-123',
      );
      expect(logger.log).toHaveBeenCalledWith('Like milestone notification', {
        recipientId: 'owner-456',
        message: '🎉 Amazing Video reached 100 likes!',
        data: expect.objectContaining({
          likeId: 'like-123',
          entityType: 'video',
          entityId: 'video-123',
          entityOwnerId: 'owner-456',
          userId: 'user-789',
          milestone: 100,
          totalLikes: 100,
          entityTitle: 'Amazing Video',
          entityThumbnail: 'https://example.com/thumb.jpg',
          timestamp: expect.any(Date),
        }),
      });
    });

    it('should handle errors in milestone notification gracefully', async () => {
      // Arrange
      const error = new Error('Database error');
      entityValidatorService.getEntityInfo.mockRejectedValue(error);

      // Act
      await service.handleLikeMilestone(milestonePayload);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to handle like milestone notification',
        {
          error: 'Database error',
          payload: {
            likeId: 'like-123',
            entityType: 'video',
            entityId: 'video-123',
            userId: 'user-789',
            milestone: 100,
            totalLikes: 100,
          },
        },
      );
    });
  });

  describe('message generation', () => {
    it('should generate entity liked message for video', () => {
      // Act
      const message = (service as any).generateEntityLikedMessage(
        'john_doe',
        'video',
        'Amazing Video',
      );

      // Assert
      expect(message).toBe('john_doe liked Amazing Video');
    });

    it('should generate entity liked message for image', () => {
      // Act
      const message = (service as any).generateEntityLikedMessage(
        'jane_doe',
        'image',
        'Beautiful Art',
      );

      // Assert
      expect(message).toBe('jane_doe liked Beautiful Art');
    });

    it('should generate entity liked message with default title', () => {
      // Act
      const message = (service as any).generateEntityLikedMessage(
        'user123',
        'video',
        null,
      );

      // Assert
      expect(message).toBe('user123 liked your video');
    });

    it('should generate milestone message for video', () => {
      // Act
      const message = (service as any).generateMilestoneMessage(
        100,
        'video',
        'Amazing Video',
      );

      // Assert
      expect(message).toBe('🎉 Amazing Video reached 100 likes!');
    });

    it('should generate milestone message for image', () => {
      // Act
      const message = (service as any).generateMilestoneMessage(
        50,
        'image',
        'Beautiful Art',
      );

      // Assert
      expect(message).toBe('🎉 Beautiful Art reached 50 likes!');
    });

    it('should generate milestone message with default title', () => {
      // Act
      const message = (service as any).generateMilestoneMessage(
        25,
        'video',
        null,
      );

      // Assert
      expect(message).toBe('🎉 your video reached 25 likes!');
    });
  });

  describe('milestone detection', () => {
    it('should detect milestone for standard milestones', () => {
      // Act & Assert
      expect((service as any).isMilestone(10)).toBe(true);
      expect((service as any).isMilestone(25)).toBe(true);
      expect((service as any).isMilestone(50)).toBe(true);
      expect((service as any).isMilestone(100)).toBe(true);
      expect((service as any).isMilestone(500)).toBe(true);
      expect((service as any).isMilestone(1000)).toBe(true);
    });

    it('should not detect milestone for non-milestone numbers', () => {
      // Act & Assert
      expect((service as any).isMilestone(1)).toBe(true);
      expect((service as any).isMilestone(15)).toBe(false);
      expect((service as any).isMilestone(75)).toBe(false);
      expect((service as any).isMilestone(150)).toBe(false);
      expect((service as any).isMilestone(999)).toBe(false);
    });

    it('should detect milestone for large round numbers', () => {
      // Act & Assert
      expect((service as any).isMilestone(5000)).toBe(true);
      expect((service as any).isMilestone(10000)).toBe(true);
      expect((service as any).isMilestone(50000)).toBe(false);
      expect((service as any).isMilestone(100000)).toBe(false);
    });
  });
});
