import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { EntityValidatorService } from '../service/entity-validator.service';
import { UserProvider } from '../../user/service/provider';
import { Notifier } from '../../notification/service/notifier';
import { EntityCommentedNotification } from './entity-commented.notification';
import { CommentLikedNotification } from './comment-liked.notification';

/**
 * Base notification data for comment-related notifications
 */
export interface BaseCommentNotificationData {
  commentId: string;
  entityType: string;
  entityId: string;
  userId: string;
  timestamp: Date;
}

/**
 * Notification data for new comment notifications
 */
export interface CommentCreatedNotificationData
  extends BaseCommentNotificationData {
  comment: string;
  entityTitle?: string;
  entityThumbnail?: string;
  entityOwnerId: string;
}

/**
 * Notification data for comment like notifications
 */
export interface CommentLikedNotificationData
  extends BaseCommentNotificationData {
  commentLikeId: string;
  commentOwnerId: string;
  commentText?: string;
  entityTitle?: string;
}

/**
 * Service to handle comment-related notifications
 */
@Injectable()
export class EntityCommentNotificationService {
  constructor(
    private logger: Logger,
    private entityValidatorService: EntityValidatorService,
    private userProvider: UserProvider,
    private notifier: Notifier,
  ) {}

  /**
   * Handle comment created events
   */
  @OnEvent('entity.comment.created')
  async handleCommentCreated(payload: {
    commentId: string;
    entityType: string;
    entityId: string;
    userId: string;
    comment: string;
  }): Promise<void> {
    try {
      // Get entity information
      const entityInfo = await this.entityValidatorService.getEntityInfo(
        payload.entityType,
        payload.entityId,
      );

      // Don't notify if user is commenting on their own entity
      if (entityInfo.ownerId === payload.userId) {
        return;
      }

      // Get commenter information
      const commenter = await this.userProvider.get(payload.userId);
      if (!commenter) {
        this.logger.warn('Commenter not found for notification', {
          userId: payload.userId,
          commentId: payload.commentId,
        });
        return;
      }

      // Create notification data
      const notificationData: CommentCreatedNotificationData = {
        commentId: payload.commentId,
        entityType: payload.entityType,
        entityId: payload.entityId,
        userId: payload.userId,
        comment: payload.comment,
        entityTitle: entityInfo.title,
        entityThumbnail: entityInfo.thumbnail,
        entityOwnerId: entityInfo.ownerId,
        timestamp: new Date(),
      };

      // Generate notification message
      const message = this.generateCommentCreatedMessage(
        commenter.username,
        payload.entityType,
        entityInfo.title,
      );

      this.logger.log('Comment created notification sent', {
        recipientId: entityInfo.ownerId,
        message,
        data: notificationData,
      });
    } catch (error) {
      this.logger.error('Failed to handle comment created notification', {
        error: error.message,
        payload,
      });
    }
  }

  /**
   * Handle comment liked events
   */
  @OnEvent('entity.comment.liked')
  async handleCommentLiked(payload: {
    commentId: string;
    commentLikeId: string;
    entityType: string;
    entityId: string;
    userId: string;
    commentOwnerId: string;
  }): Promise<void> {
    try {
      // Don't notify if user is liking their own comment
      if (payload.commentOwnerId === payload.userId) {
        return;
      }

      // Get liker information
      const liker = await this.userProvider.get(payload.userId);
      if (!liker) {
        this.logger.warn('Liker not found for notification', {
          userId: payload.userId,
          commentId: payload.commentId,
        });
        return;
      }

      // Get entity information for context
      const entityInfo = await this.entityValidatorService.getEntityInfo(
        payload.entityType,
        payload.entityId,
      );

      // Create notification data
      const notificationData: CommentLikedNotificationData = {
        commentId: payload.commentId,
        commentLikeId: payload.commentLikeId,
        entityType: payload.entityType,
        entityId: payload.entityId,
        userId: payload.userId,
        commentOwnerId: payload.commentOwnerId,
        entityTitle: entityInfo.title,
        timestamp: new Date(),
      };

      // Generate notification message
      const message = this.generateCommentLikedMessage(
        liker.username,
        payload.entityType,
        entityInfo.title,
      );

      // Dispatch notification
      await this.notifier.dispatch(
        new CommentLikedNotification(payload.commentOwnerId, notificationData),
      );

      this.logger.log('Comment liked notification sent', {
        recipientId: payload.commentOwnerId,
        message,
        data: notificationData,
      });
    } catch (error) {
      this.logger.error('Failed to handle comment liked notification', {
        error: error.message,
        payload,
      });
    }
  }

  /**
   * Generate message for comment created notifications
   */
  private generateCommentCreatedMessage(
    commenterUsername: string,
    entityType: string,
    entityTitle?: string,
  ): string {
    const entityName = entityTitle || `your ${entityType}`;
    return `${commenterUsername} commented on ${entityName}`;
  }

  /**
   * Generate message for comment liked notifications
   */
  private generateCommentLikedMessage(
    likerUsername: string,
    entityType: string,
    entityTitle?: string,
  ): string {
    const entityName = entityTitle || `a ${entityType}`;
    return `${likerUsername} liked your comment on ${entityName}`;
  }

  /**
   * Get notification type for comment events
   */
  getNotificationType(eventType: string): string {
    switch (eventType) {
      case 'comment.created':
        return 'COMMENT_CREATED';
      case 'comment.liked':
        return 'COMMENT_LIKED';
      case 'comment.unliked':
        return 'COMMENT_UNLIKED';
      default:
        return 'COMMENT_UNKNOWN';
    }
  }

  /**
   * Check if user should receive notifications for this entity type
   */
  private async shouldNotifyUser(
    userId: string,
    entityType: string,
    notificationType: string,
  ): Promise<boolean> {
    // TODO: Implement user notification preferences check
    // This would check user settings to see if they want notifications
    // for this type of event on this type of entity
    return true;
  }
}
