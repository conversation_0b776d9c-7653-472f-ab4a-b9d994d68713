import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Request,
  HttpCode,
  ParseUUI<PERSON>ipe,
  UseGuards,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityCommentLikeProvider } from '../service/entity-comment-like.provider';
import { EntityCommentLikeRequestManager } from '../service/entity-comment-like.request-manager';
import { EntityCommentLikeResponseMapper } from '../service/entity-comment-like.response-mapper';
import { EntityLikeSearchRequest } from '../dto/entity-like.search.request';
import { EntityCommentLikeDto } from '../dto/entity-comment-like.dto';

@ApiTags('Comment Likes')
@Controller('social/comments/:commentId/likes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EntityCommentLikeController {
  constructor(
    private provider: EntityCommentLikeProvider,
    private requestManager: EntityCommentLikeRequestManager,
    private responseMapper: EntityCommentLikeResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'comment_likes_list',
    summary: 'Get likes for a comment',
    description: 'Retrieve paginated likes for a specific comment.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment likes retrieved successfully',
    type: [EntityCommentLikeDto],
  })
  async findCommentLikes(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Query() query: EntityLikeSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Get comment likes
    const likes = await this.requestManager.getLikesForComment(
      commentId,
      query.page,
      query.limit,
      query.sortBy,
      query.sortOrder,
    );

    // Get total count for pagination
    const total = await this.requestManager.countLikesForComment(commentId);

    // Map response with pagination
    const response = await this.responseMapper.mapWithPagination(
      likes,
      total,
      query.page,
      query.limit,
      query.includeEntity, // includeComment
      request.user?.id,
    );

    // Set pagination headers
    res.set({
      'X-Total-Count': total.toString(),
      'X-Page': query.page.toString(),
      'X-Limit': query.limit.toString(),
      'X-Total-Pages': response.pagination.totalPages.toString(),
    });

    res.json(response.data);
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({
    operationId: 'comment_like_create',
    summary: 'Like a comment',
    description: 'Add a like to the specified comment.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Comment liked successfully',
    type: EntityCommentLikeDto,
  })
  async likeComment(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<EntityCommentLikeDto> {
    // Like comment
    const like = await this.requestManager.likeComment(
      commentId,
      request.user.id,
    );

    // Map and return response
    return this.responseMapper.map(like, false, request.user.id);
  }

  @Delete()
  @HttpCode(204)
  @ApiOperation({
    operationId: 'comment_like_delete',
    summary: 'Unlike a comment',
    description: 'Remove like from the specified comment.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Comment unliked successfully',
  })
  async unlikeComment(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<void> {
    // Unlike comment
    await this.requestManager.unlikeComment(commentId, request.user.id);
  }

  @Post('toggle')
  @HttpCode(200)
  @ApiOperation({
    operationId: 'comment_like_toggle',
    summary: 'Toggle like status for a comment',
    description: 'Like the comment if not liked, unlike if already liked.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment like status toggled successfully',
  })
  async toggleCommentLike(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<{ liked: boolean; like?: EntityCommentLikeDto }> {
    // Toggle comment like
    const result = await this.requestManager.toggleCommentLike(
      commentId,
      request.user.id,
    );

    // Map response
    const response: { liked: boolean; like?: EntityCommentLikeDto } = {
      liked: result.liked,
    };

    if (result.like) {
      response.like = await this.responseMapper.map(
        result.like,
        false,
        request.user.id,
      );
    }

    return response;
  }

  @Get('status')
  @ApiOperation({
    operationId: 'comment_like_status',
    summary: 'Check if user has liked a comment',
    description:
      'Check whether the current user has liked the specified comment.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment like status retrieved successfully',
  })
  async getCommentLikeStatus(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<{ liked: boolean }> {
    // Check like status
    const liked = await this.requestManager.hasUserLikedComment(
      commentId,
      request.user.id,
    );

    return { liked };
  }

  @Get('batch-status')
  @ApiOperation({
    operationId: 'comments_like_batch_status',
    summary: 'Check like status for multiple comments',
    description: 'Check whether the current user has liked multiple comments.',
  })
  @ApiQuery({
    name: 'commentIds',
    description: 'Comma-separated list of comment UUIDs',
    example:
      '123e4567-e89b-12d3-a456-************,456e7890-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment like statuses retrieved successfully',
  })
  async getBatchCommentLikeStatus(
    @Query('commentIds') commentIdsParam: string,
    @Request() request,
  ): Promise<{ commentId: string; liked: boolean }[]> {
    // Parse comment IDs
    const commentIds = commentIdsParam.split(',').map((id) => id.trim());

    // Check like statuses
    const statuses = await this.requestManager.checkUserLikesForComments(
      request.user.id,
      commentIds,
    );

    return this.responseMapper.mapUserLikeStatus(statuses);
  }
}
