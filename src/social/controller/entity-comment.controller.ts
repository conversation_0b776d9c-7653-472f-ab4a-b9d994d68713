import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  Query,
  Request,
  HttpCode,
  ParseUUIDPipe,
  UseG<PERSON>s,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityCommentProvider } from '../service/entity-comment.provider';
import { EntityCommentRequestManager } from '../service/entity-comment.request-manager';
import { EntityCommentResponseMapper } from '../service/entity-comment.response-mapper';
import { EntityValidatorService } from '../service/entity-validator.service';
import { EntityCommentRequest } from '../dto/entity-comment.request';
import { EntityCommentSearchRequest } from '../dto/entity-comment.search.request';
import { EntityCommentDto } from '../dto/entity-comment.dto';

@ApiTags('Entity Comments')
@Controller('social/:entityType/:entityId/comments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EntityCommentController {
  constructor(
    private provider: EntityCommentProvider,
    private requestManager: EntityCommentRequestManager,
    private responseMapper: EntityCommentResponseMapper,
    private validatorService: EntityValidatorService,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'entity_comments_list',
    summary: 'Get comments for an entity',
    description: 'Retrieve paginated comments for a specific entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comments retrieved successfully',
    type: [EntityCommentDto],
  })
  async findComments(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Query() query: EntityCommentSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Validate entity exists
    await this.validatorService.validateEntityExists(entityType, entityId);

    // Get comments
    const comments = await this.requestManager.getCommentsForEntity(
      entityType,
      entityId,
      query.page,
      query.limit,
      query.sortBy,
      query.sortOrder,
    );

    // Get total count for pagination
    const total = await this.requestManager.countCommentsForEntity(
      entityType,
      entityId,
    );

    // Map response with pagination
    const response = await this.responseMapper.mapWithPagination(
      comments,
      total,
      query.page,
      query.limit,
      request.user?.id,
    );

    // Set pagination headers
    res.set({
      'X-Total-Count': total.toString(),
      'X-Page': query.page.toString(),
      'X-Limit': query.limit.toString(),
      'X-Total-Pages': response.pagination.totalPages.toString(),
    });

    res.json(response.data);
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({
    operationId: 'entity_comment_create',
    summary: 'Create a comment on an entity',
    description: 'Add a new comment to the specified entity.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Comment created successfully',
    type: EntityCommentDto,
  })
  async createComment(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Body() commentRequest: EntityCommentRequest,
    @Request() request,
  ): Promise<EntityCommentDto> {
    // Get entity validator
    const entityValidator =
      this.validatorService.createEntityValidator(entityType);

    // Create comment
    const comment = await this.requestManager.createComment(
      entityType,
      entityId,
      request.user.id,
      commentRequest,
      entityValidator,
    );

    // Map and return response
    return this.responseMapper.map(comment, request.user.id);
  }

  @Delete(':commentId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'entity_comment_delete',
    summary: 'Delete a comment',
    description:
      'Delete the specified comment. Users can only delete their own comments.',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Comment deleted successfully',
  })
  async deleteComment(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<void> {
    // Get entity validator
    const entityValidator =
      this.validatorService.createEntityValidator(entityType);

    // Delete comment
    await this.requestManager.deleteComment(
      commentId,
      request.user.id,
      entityValidator,
    );
  }
}
