import {
  <PERSON>,
  Get,
  Param,
  Parse<PERSON><PERSON><PERSON>ipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityCommentRequestManager } from '../service/entity-comment.request-manager';
import { EntityLikeRequestManager } from '../service/entity-like.request-manager';
import { EntityValidatorService } from '../service/entity-validator.service';

@ApiTags('Social - Entity Statistics')
@Controller('social/:entityType/:entityId/stats')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EntityStatsController {
  constructor(
    private commentRequestManager: EntityCommentRequestManager,
    private likeRequestManager: EntityLikeRequestManager,
    private validatorService: EntityValidatorService,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'social_entity_stats',
    summary: 'Get entity social statistics',
    description:
      'Get combined social statistics for an entity (comment count, like count, etc.).',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Type of entity (e.g., video, image, model)',
    example: 'video',
  })
  @ApiParam({
    name: 'entityId',
    description: 'UUID of the entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Entity social statistics retrieved successfully',
  })
  async getEntityStats(
    @Param('entityType') entityType: string,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
  ): Promise<{
    entityType: string;
    entityId: string;
    commentCount: number;
    likeCount: number;
    totalEngagement: number;
  }> {
    // Validate entity exists
    await this.validatorService.validateEntityExists(entityType, entityId);

    // Get counts
    const [commentCount, likeCount] = await Promise.all([
      this.commentRequestManager.countCommentsForEntity(entityType, entityId),
      this.likeRequestManager.countLikesForEntity(entityType, entityId),
    ]);

    return {
      entityType,
      entityId,
      commentCount,
      likeCount,
      totalEngagement: commentCount + likeCount,
    };
  }
}
