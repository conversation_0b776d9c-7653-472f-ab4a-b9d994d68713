import { UserLikesRankingRequestManager } from '../service/user-likes-ranking.request-manager';
import { UserLikesRankingSearchRequest } from '../dto/user-likes-ranking.search.request';

describe('UserLikesRankingRequestManager', () => {
  let requestManager: UserLikesRankingRequestManager;

  const mockEntityLikeProvider = {
    findUserLikesRanking: jest.fn(),
    countUserLikesRanking: jest.fn(),
  };

  const mockEntityLikeResponseMapper = {
    mapUserLikesRank: jest.fn(),
    mapUserLikesRankMultiple: jest.fn(),
  };

  beforeEach(() => {
    requestManager = new UserLikesRankingRequestManager(
      mockEntityLikeProvider as any,
      mockEntityLikeResponseMapper as any,
    );
  });

  it('should be defined', () => {
    expect(requestManager).toBeDefined();
  });

  describe('generateUserLikesRanking', () => {
    it('should generate user likes ranking with default time range', async () => {
      const query: UserLikesRankingSearchRequest = {
        page: 1,
        limit: 25,
        day: false,
        week: false,
        month: false,
        year: false,
      };

      const mockRankingEntities = [
        {
          user_id: 'user-1',
          likes_count: '42',
          rank: '1',
        },
      ];

      const mockMappedData = [
        {
          rank: 1,
          user: {
            id: 'user-1',
            username: 'testuser1',
            name: 'Test User 1',
          },
          userId: 'user-1',
          likes: 42,
        },
      ];

      mockEntityLikeProvider.findUserLikesRanking.mockResolvedValue(
        mockRankingEntities,
      );
      mockEntityLikeProvider.countUserLikesRanking.mockResolvedValue(1);
      mockEntityLikeResponseMapper.mapUserLikesRankMultiple.mockResolvedValue(
        mockMappedData,
      );

      const result = await requestManager.generateUserLikesRanking(query);

      expect(result.dtos).toEqual(mockMappedData);
      expect(result.totalCount).toBe(1);
      expect(mockEntityLikeProvider.findUserLikesRanking).toHaveBeenCalled();
      expect(mockEntityLikeProvider.countUserLikesRanking).toHaveBeenCalled();
    });

    it('should handle day filter', async () => {
      const query: UserLikesRankingSearchRequest = {
        page: 1,
        limit: 25,
        day: true,
        week: false,
        month: false,
        year: false,
      };

      mockEntityLikeProvider.findUserLikesRanking.mockResolvedValue([]);
      mockEntityLikeProvider.countUserLikesRanking.mockResolvedValue(0);
      mockEntityLikeResponseMapper.mapUserLikesRankMultiple.mockResolvedValue(
        [],
      );

      const result = await requestManager.generateUserLikesRanking(query);

      expect(result.dtos).toEqual([]);
      expect(result.totalCount).toBe(0);

      // Verify that the start date was set to 1 day ago
      const callArgs =
        mockEntityLikeProvider.findUserLikesRanking.mock.calls[0];
      const startDate = new Date(callArgs[0]);
      const now = new Date();
      const daysDiff =
        (now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      expect(daysDiff).toBeCloseTo(1, 0);
    });

    it('should handle user-specific filtering', async () => {
      const query: UserLikesRankingSearchRequest = {
        page: 1,
        limit: 25,
        userId: 'specific-user-id',
        day: false,
        week: false,
        month: false,
        year: false,
      };

      const mockUserEntity = {
        user_id: 'specific-user-id',
        likes_count: '15',
        rank: '5',
      };

      const mockMappedUserData = {
        rank: 5,
        user: {
          id: 'specific-user-id',
          username: 'specificuser',
          name: 'Specific User',
        },
        userId: 'specific-user-id',
        likes: 15,
      };

      mockEntityLikeProvider.findUserLikesRanking.mockResolvedValue([
        mockUserEntity,
      ]);
      mockEntityLikeProvider.countUserLikesRanking.mockResolvedValue(1);
      mockEntityLikeResponseMapper.mapUserLikesRank.mockResolvedValue(
        mockMappedUserData,
      );

      const result = await requestManager.generateUserLikesRanking(query);

      expect(result.dtos).toEqual(mockMappedUserData);
      expect(result.totalCount).toBe(1);
      expect(
        mockEntityLikeResponseMapper.mapUserLikesRank,
      ).toHaveBeenCalledWith(mockUserEntity);
    });

    it('should handle user with no likes', async () => {
      const query: UserLikesRankingSearchRequest = {
        page: 1,
        limit: 25,
        userId: 'user-with-no-likes',
        day: false,
        week: false,
        month: false,
        year: false,
      };

      const mockEmptyUserData = {
        rank: null,
        user: {
          id: 'user-with-no-likes',
          username: 'nolikesuser',
          name: 'No Likes User',
        },
        userId: 'user-with-no-likes',
        likes: 0,
      };

      mockEntityLikeProvider.findUserLikesRanking.mockResolvedValue([]);
      mockEntityLikeProvider.countUserLikesRanking.mockResolvedValue(0);
      mockEntityLikeResponseMapper.mapUserLikesRank.mockResolvedValue(
        mockEmptyUserData,
      );

      const result = await requestManager.generateUserLikesRanking(query);

      expect(result.dtos).toEqual(mockEmptyUserData);
      expect(result.totalCount).toBe(0);

      // Verify that a default entity was created for the user with no likes
      const expectedEntity = {
        user_id: 'user-with-no-likes',
        likes_count: 0,
        rank: null,
      };
      expect(
        mockEntityLikeResponseMapper.mapUserLikesRank,
      ).toHaveBeenCalledWith(expectedEntity);
    });
  });
});
