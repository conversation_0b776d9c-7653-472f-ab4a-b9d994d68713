import {
  <PERSON>,
  Get,
  Param,
  Query,
  Request,
  ParseUUIDPipe,
  UseGuards,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
  ApiOkResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityCommentProvider } from '../service/entity-comment.provider';
import { EntityLikeProvider } from '../service/entity-like.provider';
import { EntityCommentResponseMapper } from '../service/entity-comment.response-mapper';
import { EntityLikeResponseMapper } from '../service/entity-like.response-mapper';
import { UserLikesRankingRequestManager } from '../service/user-likes-ranking.request-manager';
import { EntityCommentSearchRequest } from '../dto/entity-comment.search.request';
import { EntityLikeSearchRequest } from '../dto/entity-like.search.request';
import { EntityLikeCursorSearchRequest } from '../dto/entity-like-cursor.search.request';
import { UserLikesRankingSearchRequest } from '../dto/user-likes-ranking.search.request';
import { EntityCommentDto } from '../dto/entity-comment.dto';
import { EntityLikeDto } from '../dto/entity-like.dto';
import { EntityLikeCursorResponseDto } from '../dto/entity-like-cursor.response';
import { UserLikesRankDto } from '../dto/user-likes-rank.dto';
import { BaseFindResponseHeadersDto } from '../../core/dto/base-find-response-headers.dto';
import { setPaginationHeaders } from '../../core/utils/pagination';
import { AuthOptional, Public } from '../../core/security/public-routes';

@ApiTags('Social - User Activity')
@Controller('social/users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserActivityController {
  constructor(
    private commentProvider: EntityCommentProvider,
    private likeProvider: EntityLikeProvider,
    private commentResponseMapper: EntityCommentResponseMapper,
    private likeResponseMapper: EntityLikeResponseMapper,
    private userLikesRankingRequestManager: UserLikesRankingRequestManager,
  ) {}

  @Get(':userId/comments')
  @ApiOperation({
    operationId: 'social_user_comments',
    summary: 'Get user comments',
    description:
      'Retrieve all comments made by a specific user across all entities.',
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User comments retrieved successfully',
    type: [EntityCommentDto],
  })
  async getUserComments(
    @Param('userId', new ParseUUIDPipe()) userId: string,
    @Query() query: EntityCommentSearchRequest,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Get user comments
    const comments = await this.commentProvider.findCommentsByUser(
      userId,
      query.page,
      query.limit,
      query.sortBy,
      query.sortOrder,
    );

    // Get total count for pagination
    const total = await this.commentProvider.countCommentsByUser(userId);

    // Map response with pagination
    const response = await this.commentResponseMapper.mapWithPagination(
      comments,
      total,
      query.page,
      query.limit,
      request.user?.id,
    );

    // Set pagination headers
    res.set({
      'X-Total-Count': total.toString(),
      'X-Page': query.page.toString(),
      'X-Limit': query.limit.toString(),
      'X-Total-Pages': response.pagination.totalPages.toString(),
    });

    res.json(response.data);
  }

  @Get(':userId/likes')
  @ApiOperation({
    operationId: 'social_user_likes',
    summary: 'Get user likes',
    description:
      'Retrieve all likes made by a specific user across all entities. ' +
      'Supports both offset-based pagination (page/limit) and cursor-based pagination (cursor/limit). ' +
      'If cursor parameter is provided, cursor-based pagination is used for better performance.',
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'cursor',
    description:
      'Cursor for pagination (timestamp or ID of last item from previous page)',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  @ApiQuery({
    name: 'page',
    description:
      'Page number for offset-based pagination (ignored if cursor is provided)',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    example: 20,
    required: false,
  })
  @ApiQuery({
    name: 'modelIds',
    description: 'Array of model IDs to filter liked content by specific AI models',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
    ],
    required: false,
  })
  @ApiResponse({
    status: 200,
    description:
      'User likes retrieved successfully (format depends on pagination type)',
    schema: {
      oneOf: [
        {
          type: 'array',
          items: { $ref: '#/components/schemas/EntityLikeDto' },
          description: 'Offset-based pagination response (legacy format)',
        },
        {
          $ref: '#/components/schemas/EntityLikeCursorResponseDto',
          description: 'Cursor-based pagination response',
        },
      ],
    },
  })
  async getUserLikes(
    @Param('userId', new ParseUUIDPipe()) userId: string,
    @Query() query: any, // Accept both pagination types
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Check if cursor-based pagination is requested (cursor parameter exists, even if empty)
    if (query.cursor !== undefined) {
      // Use cursor-based pagination
      const cursorQuery = new EntityLikeCursorSearchRequest();
      Object.assign(cursorQuery, query);

      const likes = await this.likeProvider.findLikesByUserWithCursor(
        userId,
        cursorQuery.cursor && cursorQuery.cursor.trim() !== ''
          ? cursorQuery.cursor
          : undefined,
        cursorQuery.limit,
        cursorQuery.sortBy,
        cursorQuery.sortOrder,
        cursorQuery.modelIds,
      );

      // Map response with cursor pagination - always include entity information for user likes
      const response = await this.likeResponseMapper.mapWithCursorPagination(
        likes,
        cursorQuery.limit,
        true, // Always include entity information for user likes endpoint
        cursorQuery.sortBy,
      );

      // Set cursor pagination headers
      res.set({
        'X-Pagination-Type': 'cursor',
        'X-Count': response.pagination.count.toString(),
        'X-Has-More': response.pagination.hasMore.toString(),
        'X-Next-Cursor': response.pagination.nextCursor || '',
      });

      res.json(response);
    } else {
      // Use legacy offset-based pagination for backward compatibility
      const offsetQuery = new EntityLikeSearchRequest();
      Object.assign(offsetQuery, query);

      const likes = await this.likeProvider.findLikesByUser(
        userId,
        offsetQuery.page,
        offsetQuery.limit,
        offsetQuery.sortBy,
        offsetQuery.sortOrder,
        offsetQuery.modelIds,
      );

      // Get total count for pagination
      const total = await this.likeProvider.countLikesByUser(userId);

      // Map response with pagination - always include entity information for user likes
      const response = await this.likeResponseMapper.mapWithPagination(
        likes,
        total,
        offsetQuery.page,
        offsetQuery.limit,
        true, // Always include entity information for user likes endpoint
      );

      // Set legacy pagination headers
      res.set({
        'X-Pagination-Type': 'offset',
        'X-Total-Count': total.toString(),
        'X-Page': offsetQuery.page.toString(),
        'X-Limit': offsetQuery.limit.toString(),
        'X-Total-Pages': response.pagination.totalPages.toString(),
      });

      res.json(response);
    }
  }

  @Get(':userId/activity')
  @ApiOperation({
    operationId: 'social_user_activity',
    summary: 'Get user activity feed',
    description:
      'Retrieve combined activity feed of comments and likes for a specific user.',
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    example: 1,
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    example: 20,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'User activity retrieved successfully',
  })
  async getUserActivity(
    @Param('userId', new ParseUUIDPipe()) userId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Request() request,
    @Res() res: Response,
  ): Promise<void> {
    // Get recent comments and likes
    const [comments, likes] = await Promise.all([
      this.commentProvider.findCommentsByUser(userId, 1, Math.ceil(limit / 2)),
      this.likeProvider.findLikesByUser(userId, 1, Math.ceil(limit / 2)),
    ]);

    // Map responses
    const [mappedComments, mappedLikes] = await Promise.all([
      this.commentResponseMapper.mapMultiple(comments, request.user?.id),
      this.likeResponseMapper.mapMultiple(likes, true), // include entity info
    ]);

    // Combine and sort by creation date
    const activity = [
      ...mappedComments.map((comment) => ({
        type: 'comment',
        id: comment.id,
        entityType: comment.entityType,
        entityId: comment.entityId,
        createdAt: comment.createdAt,
        data: comment,
      })),
      ...mappedLikes.map((like) => ({
        type: 'like',
        id: like.id,
        entityType: like.entityType,
        entityId: like.entityId,
        createdAt: like.createdAt,
        data: like,
      })),
    ]
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      )
      .slice(0, limit);

    // Get total counts for pagination
    const [totalComments, totalLikes] = await Promise.all([
      this.commentProvider.countCommentsByUser(userId),
      this.likeProvider.countLikesByUser(userId),
    ]);

    const total = totalComments + totalLikes;
    const totalPages = Math.ceil(total / limit);

    // Set pagination headers
    res.set({
      'X-Total-Count': total.toString(),
      'X-Page': page.toString(),
      'X-Limit': limit.toString(),
      'X-Total-Pages': totalPages.toString(),
    });

    res.json({
      activity,
      stats: {
        totalComments,
        totalLikes,
        totalActivity: total,
      },
    });
  }

  @Get(':userId/stats')
  @ApiOperation({
    operationId: 'social_user_stats',
    summary: 'Get user social statistics',
    description:
      'Get overall social statistics for a user (comment count, like count, etc.).',
  })
  @ApiParam({
    name: 'userId',
    description: 'UUID of the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User social statistics retrieved successfully',
  })
  async getUserStats(
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<{
    userId: string;
    commentCount: number;
    likeCount: number;
    totalActivity: number;
  }> {
    // Get counts
    const [commentCount, likeCount] = await Promise.all([
      this.commentProvider.countCommentsByUser(userId),
      this.likeProvider.countLikesByUser(userId),
    ]);

    return {
      userId,
      commentCount,
      likeCount,
      totalActivity: commentCount + likeCount,
    };
  }

  @Get('likes-ranking')
  @ApiOperation({
    operationId: 'social_users_likes_ranking',
    summary: 'Get user likes ranking',
    description:
      'Retrieves a paginated ranking of users by number of likes they have received across all entities.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- day: Filter by likes received today\n' +
      '- week: Filter by likes received this week\n' +
      '- month: Filter by likes received this month\n' +
      '- year: Filter by likes received this year\n' +
      '- userId: Filter by specific user ID\n',
  })
  @ApiOkResponse({
    type: UserLikesRankDto,
    isArray: true,
    headers: BaseFindResponseHeadersDto.toSwaggerHeaders(),
    description: 'Paginated user likes ranking.',
  })
  @ApiQuery({
    type: UserLikesRankingSearchRequest,
    description: 'Query parameters for user likes ranking.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid query parameters.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  @AuthOptional()
  @Public()
  async getUserLikesRanking(
    @Res() res: Response,
    @Query() query: UserLikesRankingSearchRequest,
  ): Promise<void> {
    const { dtos, totalCount } =
      await this.userLikesRankingRequestManager.generateUserLikesRanking(query);

    setPaginationHeaders(res, totalCount, query.page, query.limit);

    res.send(dtos);
  }
}
