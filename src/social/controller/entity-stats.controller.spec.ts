import { Test, TestingModule } from '@nestjs/testing';
import { EntityStatsController } from './entity-stats.controller';
import { EntityCommentRequestManager } from '../service/entity-comment.request-manager';
import { EntityLikeRequestManager } from '../service/entity-like.request-manager';
import { EntityValidatorService } from '../service/entity-validator.service';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';

describe('EntityStatsController', () => {
  let controller: EntityStatsController;
  let commentRequestManager: jest.Mocked<EntityCommentRequestManager>;
  let likeRequestManager: jest.Mocked<EntityLikeRequestManager>;
  let validatorService: jest.Mocked<EntityValidatorService>;

  beforeEach(async () => {
    const mockCommentRequestManager = {
      countCommentsForEntity: jest.fn(),
    };

    const mockLikeRequestManager = {
      countLikesForEntity: jest.fn(),
    };

    const mockValidatorService = {
      validateEntityExists: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EntityStatsController],
      providers: [
        {
          provide: EntityCommentRequestManager,
          useValue: mockCommentRequestManager,
        },
        {
          provide: EntityLikeRequestManager,
          useValue: mockLikeRequestManager,
        },
        {
          provide: EntityValidatorService,
          useValue: mockValidatorService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<EntityStatsController>(EntityStatsController);
    commentRequestManager = module.get(EntityCommentRequestManager);
    likeRequestManager = module.get(EntityLikeRequestManager);
    validatorService = module.get(EntityValidatorService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getEntityStats', () => {
    it('should return entity stats successfully', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'entity-1';
      const commentCount = 5;
      const likeCount = 10;

      validatorService.validateEntityExists.mockResolvedValue(undefined);
      commentRequestManager.countCommentsForEntity.mockResolvedValue(
        commentCount,
      );
      likeRequestManager.countLikesForEntity.mockResolvedValue(likeCount);

      // Act
      const result = await controller.getEntityStats(entityType, entityId);

      // Assert
      expect(result).toEqual({
        entityType,
        entityId,
        commentCount,
        likeCount,
        totalEngagement: commentCount + likeCount,
      });
      expect(validatorService.validateEntityExists).toHaveBeenCalledWith(
        entityType,
        entityId,
      );
      expect(commentRequestManager.countCommentsForEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
      );
      expect(likeRequestManager.countLikesForEntity).toHaveBeenCalledWith(
        entityType,
        entityId,
      );
    });

    it('should handle zero counts', async () => {
      // Arrange
      const entityType = 'video';
      const entityId = 'entity-1';
      const commentCount = 0;
      const likeCount = 0;

      validatorService.validateEntityExists.mockResolvedValue(undefined);
      commentRequestManager.countCommentsForEntity.mockResolvedValue(
        commentCount,
      );
      likeRequestManager.countLikesForEntity.mockResolvedValue(likeCount);

      // Act
      const result = await controller.getEntityStats(entityType, entityId);

      // Assert
      expect(result).toEqual({
        entityType,
        entityId,
        commentCount,
        likeCount,
        totalEngagement: 0,
      });
    });

    it('should handle different entity types', async () => {
      // Arrange
      const entityType = 'image';
      const entityId = 'entity-1';
      const commentCount = 8;
      const likeCount = 15;

      validatorService.validateEntityExists.mockResolvedValue(undefined);
      commentRequestManager.countCommentsForEntity.mockResolvedValue(
        commentCount,
      );
      likeRequestManager.countLikesForEntity.mockResolvedValue(likeCount);

      // Act
      const result = await controller.getEntityStats(entityType, entityId);

      // Assert
      expect(result).toEqual({
        entityType,
        entityId,
        commentCount,
        likeCount,
        totalEngagement: commentCount + likeCount,
      });
    });
  });
});
