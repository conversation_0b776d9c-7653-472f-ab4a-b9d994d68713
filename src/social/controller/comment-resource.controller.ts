import {
  Controller,
  Get,
  Delete,
  Param,
  Request,
  HttpCode,
  ParseUUI<PERSON>ipe,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/service/jwt-auth.guard';
import { EntityCommentProvider } from '../service/entity-comment.provider';
import { EntityCommentRequestManager } from '../service/entity-comment.request-manager';
import { EntityCommentResponseMapper } from '../service/entity-comment.response-mapper';
import { EntityValidatorService } from '../service/entity-validator.service';
import { EntityCommentDto } from '../dto/entity-comment.dto';

@ApiTags('Social - Comments')
@Controller('social/comments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CommentResourceController {
  constructor(
    private provider: EntityCommentProvider,
    private requestManager: EntityCommentRequestManager,
    private responseMapper: EntityCommentResponseMapper,
    private validatorService: EntityValidatorService,
  ) {}

  @Get(':commentId')
  @ApiOperation({
    operationId: 'social_comment_get',
    summary: 'Get a specific comment',
    description: 'Retrieve details of a specific comment by its ID.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment retrieved successfully',
    type: EntityCommentDto,
  })
  async getComment(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<EntityCommentDto> {
    // Get comment
    const comment = await this.provider.get(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Map and return response
    return this.responseMapper.map(comment, request.user?.id);
  }

  @Delete(':commentId')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'social_comment_delete',
    summary: 'Delete a comment',
    description:
      'Delete the specified comment. Users can only delete their own comments.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment to delete',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Comment deleted successfully',
  })
  async deleteComment(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
    @Request() request,
  ): Promise<void> {
    // Get comment to determine entity type
    const comment = await this.provider.get(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    // Get entity validator
    const entityValidator = this.validatorService.createEntityValidator(
      comment.entityType,
    );

    // Delete comment
    await this.requestManager.deleteComment(
      commentId,
      request.user.id,
      entityValidator,
    );
  }

  @Get(':commentId/stats')
  @ApiOperation({
    operationId: 'social_comment_stats',
    summary: 'Get comment statistics',
    description:
      'Get like count and other statistics for the specified comment.',
  })
  @ApiParam({
    name: 'commentId',
    description: 'UUID of the comment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Comment statistics retrieved successfully',
  })
  async getCommentStats(
    @Param('commentId', new ParseUUIDPipe()) commentId: string,
  ): Promise<{ commentId: string; likeCount: number }> {
    // Get comment
    const comment = await this.provider.get(commentId);
    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    return {
      commentId,
      likeCount: comment.likes || 0,
    };
  }
}
