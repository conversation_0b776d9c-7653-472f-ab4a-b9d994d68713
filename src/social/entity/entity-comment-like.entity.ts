import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';
import { EntityCommentEntity } from './entity-comment.entity';

@Entity('entity_comment_like')
@Index('idx_entity_comment_like_comment_user', ['entityCommentId', 'userId'])
export class EntityCommentLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_entity_comment_like_comment_id')
  entityCommentId: string;

  @ManyToOne(() => EntityCommentEntity)
  entityComment: EntityCommentEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_entity_comment_like_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
