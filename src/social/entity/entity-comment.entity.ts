import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('entity_comment')
@Index('idx_entity_comment_entity_type_id', ['entityType', 'entityId'])
@Index('idx_entity_comment_user_id', ['userId'])
@Index('idx_entity_comment_created_at', ['createdAt'])
export class EntityCommentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  @Index('idx_entity_comment_entity_type')
  entityType: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_entity_comment_entity_id')
  entityId: string;

  @Column({ type: 'uuid', nullable: false })
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'text' })
  comment: string;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
