import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('entity_like')
@Index('idx_entity_like_entity_type_id', ['entityType', 'entityId'])
@Index('idx_entity_like_user_id', ['userId'])
@Index('idx_entity_like_entity_user', ['entityType', 'entityId', 'userId'])
@Index('idx_entity_like_created_at', ['createdAt'])
export class EntityLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  @Index('idx_entity_like_entity_type')
  entityType: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_entity_like_entity_id')
  entityId: string;

  @Column({ type: 'uuid', nullable: false })
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
