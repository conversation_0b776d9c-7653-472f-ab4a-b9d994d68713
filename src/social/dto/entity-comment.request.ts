import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class EntityCommentRequest {
  @ApiProperty({
    description: 'The comment text',
    example: 'This is a great video!',
    minLength: 1,
    maxLength: 2000,
  })
  @IsNotEmpty({ message: 'Comment text is required' })
  @IsString({ message: 'Comment must be a string' })
  @MinLength(1, { message: 'Comment must be at least 1 character long' })
  @MaxLength(2000, { message: 'Comment must be at most 2000 characters long' })
  comment: string;
}
