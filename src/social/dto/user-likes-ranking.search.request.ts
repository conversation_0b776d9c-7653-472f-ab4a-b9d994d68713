import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsOptional,
  IsUUI<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { BaseSearchRequest } from '../../core/dto/base.search-request';
import { Transform } from 'class-transformer';

export class UserLikesRankingSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform((obj) => Number(obj.value))
  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 25,
  })
  limit?: number = 25;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    description: 'Filter by specific user ID',
    required: false,
  })
  userId?: string;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Filter by likes received today',
    required: false,
    default: false,
  })
  day: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Filter by likes received this week',
    required: false,
    default: false,
  })
  week: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Filter by likes received this month',
    required: false,
    default: false,
  })
  month: boolean = false;

  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'Filter by likes received this year',
    required: false,
    default: false,
  })
  year: boolean = false;
}
