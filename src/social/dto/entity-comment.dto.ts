import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from '../../user/dto/public.user.dto';

export class EntityCommentDto {
  @ApiProperty({
    description: 'Unique identifier of the comment',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Type of entity this comment belongs to',
    example: 'video',
  })
  entityType: string;

  @ApiProperty({
    description: 'ID of the entity this comment belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  entityId: string;

  @ApiProperty({
    description: 'The comment text',
    example: 'This is an amazing video!',
  })
  comment: string;

  @ApiProperty({
    description: 'Number of likes this comment has received',
    example: 5,
  })
  likes: number;

  @ApiProperty({
    description: 'Whether the current user has liked this comment',
    example: true,
    required: false,
  })
  isLikedByUser?: boolean;

  @ApiProperty({
    description: 'User who created the comment',
    type: PublicUserDto,
  })
  user: PublicUserDto;

  @ApiProperty({
    description: 'Date when the comment was created',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;
}
