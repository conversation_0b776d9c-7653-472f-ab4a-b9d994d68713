import { ApiProperty } from '@nestjs/swagger';
import { EntityLikeDto } from './entity-like.dto';

export class EntityLikeCursorPaginationDto {
  @ApiProperty({
    description: 'Number of items returned in this page',
    example: 20,
  })
  count: number;

  @ApiProperty({
    description: 'Whether there are more items available',
    example: true,
  })
  hasMore: boolean;

  @ApiProperty({
    description: 'Cursor for the next page (timestamp or ID of last item)',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  nextCursor?: string;

  @ApiProperty({
    description: 'Human-readable timestamp of the next cursor',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  nextCursorTimestamp?: string;
}

export class EntityLikeCursorResponseDto {
  @ApiProperty({
    description: 'Array of entity likes',
    type: [EntityLikeDto],
  })
  items: EntityLikeDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: EntityLikeCursorPaginationDto,
  })
  pagination: EntityLikeCursorPaginationDto;
}
