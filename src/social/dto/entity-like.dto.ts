import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from '../../user/dto/public.user.dto';

export class EntityLikeDto {
  @ApiProperty({
    description: 'Unique identifier of the like',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Type of entity this like belongs to',
    example: 'video',
  })
  entityType: string;

  @ApiProperty({
    description: 'ID of the entity this like belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  entityId: string;

  @ApiProperty({
    description: 'User who created the like',
    type: PublicUserDto,
  })
  user: PublicUserDto;

  @ApiProperty({
    description: 'Date when the like was created',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Entity information (optional)',
    required: false,
  })
  entity?: any;
}
