import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class UserLikesRankDto {
  @ApiProperty({
    description: 'Ranking position of the user',
    example: '1',
  })
  rank: string;

  @ApiProperty({
    description: 'User information',
    type: PublicUserDto,
  })
  user: PublicUserDto;

  @ApiProperty({
    description: 'User ID (for convenience)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  userId?: string;

  @ApiProperty({
    description: 'Total number of likes received by the user',
    example: '42',
  })
  likes: string;
}
