import { ApiProperty } from '@nestjs/swagger';
import { PublicUserDto } from '../../user/dto/public.user.dto';
import { EntityCommentDto } from './entity-comment.dto';

export class EntityCommentLikeDto {
  @ApiProperty({
    description: 'Unique identifier of the comment like',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'ID of the comment this like belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  entityCommentId: string;

  @ApiProperty({
    description: 'User who liked the comment',
    type: PublicUserDto,
  })
  user: PublicUserDto;

  @ApiProperty({
    description: 'Date when the comment was liked',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Comment information (optional)',
    type: EntityCommentDto,
    required: false,
  })
  entityComment?: EntityCommentDto;
}
