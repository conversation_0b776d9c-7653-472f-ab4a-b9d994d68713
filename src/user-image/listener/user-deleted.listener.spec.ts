import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CleanupAuditService } from 'src/core/service/cleanup-audit.service';
import { S3BatchDeleteService } from 'src/core/service/s3-batch-delete.service';
import { UserDeletedEvent } from 'src/user/event/user-deleted.event';
import { UserImageDeletedListener } from './user-deleted.listener';
import { UserImageEntity } from '../entity/user-image.entity';

describe('UserImageDeletedListener', () => {
  let listener: UserImageDeletedListener;
  let mockUserImageRepository: Partial<Repository<UserImageEntity>>;
  let mockCleanupAuditService: Partial<CleanupAuditService>;
  let mockS3BatchDeleteService: Partial<S3BatchDeleteService>;

  beforeEach(async () => {
    mockUserImageRepository = {
      find: jest.fn(),
      softDelete: jest.fn(),
    };

    mockCleanupAuditService = {
      startOperation: jest.fn().mockReturnValue('operation-123'),
      completeOperation: jest.fn(),
      failOperation: jest.fn(),
    };

    mockS3BatchDeleteService = {
      validateS3Path: jest.fn().mockReturnValue(true),
      deleteWithRetry: jest.fn().mockResolvedValue({
        deleted: ['path1.jpg', 'path2.jpg'],
        failed: [],
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserImageDeletedListener,
        {
          provide: getRepositoryToken(UserImageEntity),
          useValue: mockUserImageRepository,
        },
        {
          provide: CleanupAuditService,
          useValue: mockCleanupAuditService,
        },
        {
          provide: S3BatchDeleteService,
          useValue: mockS3BatchDeleteService,
        },
      ],
    }).compile();

    listener = module.get<UserImageDeletedListener>(UserImageDeletedListener);
  });

  it('should be defined', () => {
    expect(listener).toBeDefined();
  });

  it('should handle user deleted event with images', async () => {
    const event = new UserDeletedEvent({
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
    });

    const mockImages = [
      {
        id: 'img1',
        userId: 'user-123',
        imagePath: 'path1.jpg',
        storageBucket: 'test-bucket',
        storagePath: 'user-123/path1.jpg',
      },
      {
        id: 'img2',
        userId: 'user-123',
        imagePath: 'path2.jpg',
        storageBucket: 'test-bucket',
        storagePath: 'user-123/path2.jpg',
      },
    ] as UserImageEntity[];

    (mockUserImageRepository.find as jest.Mock).mockResolvedValue(mockImages);
    (mockUserImageRepository.softDelete as jest.Mock).mockResolvedValue({
      affected: 2,
    });

    await listener.handleUserDeletedEvent(event);

    expect(mockCleanupAuditService.startOperation).toHaveBeenCalledWith(
      'user_images',
      'user-123',
      '<EMAIL>',
      { username: 'testuser' },
    );

    expect(mockUserImageRepository.find).toHaveBeenCalledWith({
      where: { userId: 'user-123' },
      withDeleted: true,
    });

    expect(mockS3BatchDeleteService.deleteWithRetry).toHaveBeenCalledWith(
      'test-bucket',
      ['user-123/path1.jpg', 'user-123/path2.jpg'],
      3,
    );

    expect(mockUserImageRepository.softDelete).toHaveBeenCalledWith({
      userId: 'user-123',
    });

    expect(mockCleanupAuditService.completeOperation).toHaveBeenCalledWith(
      'operation-123',
      4, // 2 images processed + 2 DB records
      2, // 2 S3 files deleted
      0, // 0 failures
    );
  });

  it('should handle user with no images', async () => {
    const event = new UserDeletedEvent({
      id: 'user-456',
      username: 'emptyuser',
      email: '<EMAIL>',
    });

    (mockUserImageRepository.find as jest.Mock).mockResolvedValue([]);
    (mockUserImageRepository.softDelete as jest.Mock).mockResolvedValue({
      affected: 0,
    });

    await listener.handleUserDeletedEvent(event);

    expect(mockS3BatchDeleteService.deleteWithRetry).not.toHaveBeenCalled();
    expect(mockCleanupAuditService.completeOperation).toHaveBeenCalledWith(
      'operation-123',
      0, // 0 processed
      0, // 0 deleted
      0, // 0 failures
    );
  });

  it('should handle S3 deletion failures', async () => {
    const event = new UserDeletedEvent({
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
    });

    const mockImages = [
      {
        id: 'img1',
        userId: 'user-123',
        imagePath: 'path1.jpg',
        storageBucket: 'test-bucket',
        storagePath: 'user-123/path1.jpg',
      },
    ] as UserImageEntity[];

    (mockUserImageRepository.find as jest.Mock).mockResolvedValue(mockImages);
    (mockUserImageRepository.softDelete as jest.Mock).mockResolvedValue({
      affected: 1,
    });

    (mockS3BatchDeleteService.deleteWithRetry as jest.Mock).mockResolvedValue({
      deleted: [],
      failed: [{ key: 'user-123/path1.jpg', error: 'Access denied' }],
    });

    await listener.handleUserDeletedEvent(event);

    expect(mockCleanupAuditService.completeOperation).toHaveBeenCalledWith(
      'operation-123',
      2, // 1 image processed + 1 DB record
      0, // 0 S3 files deleted
      1, // 1 failure
    );
  });

  it('should handle repository errors', async () => {
    const event = new UserDeletedEvent({
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
    });

    const error = new Error('Database error');
    (mockUserImageRepository.find as jest.Mock).mockRejectedValue(error);

    await expect(listener.handleUserDeletedEvent(event)).rejects.toThrow(error);

    expect(mockCleanupAuditService.failOperation).toHaveBeenCalledWith(
      'operation-123',
      'Failed to cleanup user images: Database error',
    );
  });

  it('should use default bucket when storageBucket is not set', async () => {
    const event = new UserDeletedEvent({
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
    });

    const mockImages = [
      {
        id: 'img1',
        userId: 'user-123',
        imagePath: 'path1.jpg',
        storageBucket: null,
        storagePath: 'user-123/path1.jpg',
      },
    ] as UserImageEntity[];

    (mockUserImageRepository.find as jest.Mock).mockResolvedValue(mockImages);
    (mockUserImageRepository.softDelete as jest.Mock).mockResolvedValue({
      affected: 1,
    });

    await listener.handleUserDeletedEvent(event);

    expect(mockS3BatchDeleteService.deleteWithRetry).toHaveBeenCalledWith(
      'letz-ai-images', // Default bucket
      ['user-123/path1.jpg'],
      3,
    );
  });
});
