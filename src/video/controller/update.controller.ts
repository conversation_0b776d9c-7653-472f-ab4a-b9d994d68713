import {
  Body,
  Controller,
  HttpCode,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { VideoPromptPrivacyRequest } from '../dto/video.prompt-privacy-request';
import { VideoProvider } from '../service/provider';
import { VideoRequestManager } from '../service/request-manager';
import { VideoPrivacyRequest } from '../dto/video-privacy.request';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class UpdateController {
  constructor(
    private provider: VideoProvider,
    private requestManager: VideoRequestManager,
  ) {}

  @Put(':id/interruption')
  @HttpCode(204)
  @ApiOperation({
    operationId: 'videos_interrupt',
    summary: 'Interrupt video generation',
    description:
      'Interrupts the specified video for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the video to interrupt\n',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video to interrupt',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Video interrupted successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async interruption(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ) {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.interrupt(entity);
  }

  @Put(':id/privacy')
  @HttpCode(204)
  @ApiBearerAuth()
  @ApiOperation({
    operationId: 'videos_privacy_change',
    summary: 'Update video privacy',
    description:
      'Changes the privacy settings of a video.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the video\n\n' +
      'Request Body:\n' +
      '- privacy: New privacy level (public, private, organization)\n\n' +
      'Note: Only works for videos owned by the user.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: VideoPrivacyRequest,
    description: 'Privacy update parameters.',
  })
  @ApiNoContentResponse({
    description: 'Video privacy updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID or parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async videoPrivacy(
    @Body() requestBody: VideoPrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updatePrivacy(entity, requestBody);
  }

  @Put(':id/prompt-privacy')
  @HttpCode(204)
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    operationId: 'videos_prompt_privacy_change',
    summary: 'Update video prompt privacy',
    description:
      'Changes the prompt privacy settings of a video.\n\n' +
      'Path Parameters:\n' +
      '- id: UUID of the video\n\n' +
      'Request Body:\n' +
      '- hidePrompt: Boolean indicating if the prompt should be hidden\n\n' +
      'Note: Only works for videos owned by the user.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: VideoPromptPrivacyRequest,
    description: 'Prompt privacy update parameters.',
  })
  @ApiNoContentResponse({
    description: 'Video prompt privacy updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID or parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async videoPromptPrivacy(
    @Body() requestBody: VideoPromptPrivacyRequest,
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const entity = await this.provider.getBy({
      user: { id: request.user.id },
      id: id,
    });

    await this.requestManager.updatePromptPrivacy(entity, requestBody);
  }
}
