import {
  Controller,
  Delete,
  HttpC<PERSON>,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiNoContentResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { VideoManager } from '../service/manager';
import { VideoProvider } from '../service/provider';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class DeleteController {
  constructor(private provider: VideoProvider, private manager: VideoManager) {}

  @Delete(':id')
  @HttpCode(204)
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    operationId: 'video_delete',
    summary: 'Delete a video',
    description:
      'Deletes the specified video for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the video to delete\n',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiNoContentResponse({
    description: 'Video deleted successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async delete(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const video = await this.provider.getBy({
      id,
      userId: request.user.id,
    });

    await this.manager.delete(video);
  }
}
