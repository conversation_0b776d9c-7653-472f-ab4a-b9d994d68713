import {
  <PERSON>,
  Get,
  Param,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { VideoDto } from '../../dto/video.dto';
import { VideoInternalSearchRequest } from '../../dto/video.internal-search-request';
import { VideoProvider } from '../../service/provider';
import { VideoResponseMapper } from '../../service/response-mapper';

@ApiTags('video / internal')
@Controller('internal/videos')
export class ReadController {
  constructor(
    private provider: VideoProvider,
    private responseMapper: VideoResponseMapper,
  ) {}

  @Get()
  @ApiOperation({
    operationId: 'internal_video_list',
    summary: 'List internal videos',
    description:
      'Retrieves a paginated list of videos for internal use.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of items per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- status: Filter by status\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- isNsfw: Filter by NSFW content\n' +
      '- isHot: Filter by hot content\n',
  })
  @ApiOkResponse({
    type: Array<VideoDto>,
    description: 'Paginated list of videos.',
  })
  @ApiQuery({
    type: VideoInternalSearchRequest,
    description: 'Query parameters for searching and paginating videos.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - Pagination parameters (\`page\`, \`limit\`) must be positive integers between 1 and 100.
      - Sort parameters (\`sortBy\`, \`sortOrder\`) must be valid values.
      - Filter parameters must be valid values.
      - \`status\`: Must be a valid value from the \`StatusEnum\`.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  @UsePipes(new ValidationPipe())
  async find(
    @Query() query: VideoInternalSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;

    const entities = await this.provider.findBy(
      inputFilters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(inputFilters);
    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultipleInternal(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'internal_video_get',
    summary: 'Get internal video by ID',
    description:
      'Retrieves a specific video by its UUID for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: ID of the video\n',
  })
  @ApiOkResponse({
    type: VideoDto,
    description: 'Returns the video with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the video to retrieve.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Video does not exist.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async get(@Param() params): Promise<VideoDto> {
    return this.provider
      .get(params.id)
      .then((entity) => this.responseMapper.mapInternal(entity));
  }
}
