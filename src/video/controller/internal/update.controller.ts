import { Body, Controller, Param, ParseU<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiInternalServerErrorResponse,
  ApiParam,
} from '@nestjs/swagger';
import { VideoDto } from '../../dto/video.dto';
import { VideoInternalRequest } from '../../dto/video.internal-request';
import { VideoProvider } from '../../service/provider';
import { VideoRequestManager } from '../../service/request-manager';
import { VideoResponseMapper } from '../../service/response-mapper';

@ApiTags('video / internal')
@Controller('internal/videos')
export class UpdateController {
  constructor(
    private provider: VideoProvider,
    private requestManager: VideoRequestManager,
    private responseMapper: VideoResponseMapper,
  ) {}

  @Patch(':id')
  @ApiOperation({
    operationId: 'internal_video_update',
    summary: 'Update internal video',
    description:
      'Updates the specified video for internal use.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the video to update\n\n' +
      'Optional Body Parameters:\n' +
      '- status: New status of the video\n' +
      '- progress: New progress percentage\n' +
      '- privacy: New privacy of the video\n' +
      '- storageBucket: New storage bucket\n' +
      '- storagePath: New storage path\n' +
      '- generationSeconds: Generation time in seconds\n' +
      '- isNsfw: NSFW flag\n' +
      '- isHot: Hot content flag\n' +
      '- isUnsafe: Unsafe content flag\n' +
      '- videoPaths: New video paths\n' +
      '- webhookUrl: New webhook URL\n' +
      '- hidePrompt: Hide prompt flag\n' +
      '- width: New width of the video\n' +
      '- height: New height of the video\n' +
      '- resolution: New resolution of the video\n',
  })
  @ApiOkResponse({
    type: VideoDto,
    description: 'Video updated successfully.',
  })
  @ApiBody({
    type: VideoInternalRequest,
    description: 'Video update parameters.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the video to update.',
  })
  @ApiBadRequestResponse({
    description: `Bad Request. Possible reasons:
      - \`id\`: Must be a valid UUID.
      - Video does not exist.
      - Invalid request body parameters.
      - Failed to analyze request body.
    `,
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error. Unexpected error occurred.',
  })
  async update(
    @Body() requestBody: VideoInternalRequest,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<VideoDto> {
    const entity = await this.provider.get(id);

    await this.requestManager.updateInternal(entity, requestBody);

    return await this.responseMapper.mapInternal(entity);
  }
}
