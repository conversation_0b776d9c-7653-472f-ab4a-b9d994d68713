import {
  <PERSON>,
  Get,
  Param,
  <PERSON>rse<PERSON><PERSON><PERSON>ipe,
  Query,
  Request,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { Public, AuthOptional } from 'src/core/security/public-routes';
import { CurrentUser } from 'src/auth/decorator/current-user.decorator';
import { UserEntity } from 'src/user/entity/user.entity';
import { VideoDto } from '../dto/video.dto';
import { VideoProvider } from '../service/provider';
import { VideoResponseMapper } from '../service/response-mapper';
import { VideoSearchRequest } from '../dto/video.search-request';
import { PrivacyEnum, StatusEnum } from '../entity/video.entity';

@ApiTags('videos')
@Controller('videos')
export class ReadController {
  constructor(
    private provider: VideoProvider,
    private responseMapper: VideoResponseMapper,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'video_list',
    summary: 'List videos',
    description:
      'Retrieves a paginated list of videos for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (1-50, default: 10)\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- status: Filter by status\n' +
      '- privacy: Filter by privacy\n' +
      '- isNsfw: Filter by NSFW content\n',
  })
  @ApiQuery({
    type: VideoSearchRequest,
    description: 'Query parameters for searching and paginating videos',
  })
  @ApiOkResponse({
    type: [VideoDto],
    description: 'Paginated list of videos',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async find(
    @Request() request,
    @Query() query: VideoSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = {
      ...inputFilters,
      userId: request.user.id,
    };

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @AuthOptional()
  @ApiOperation({
    operationId: 'video_get',
    summary: 'Get video by ID',
    description:
      'Retrieves the details of a specific video by its unique identifier.\n\n' +
      'This endpoint is publicly accessible but respects privacy settings:\n' +
      '- Public videos: Accessible to everyone\n' +
      '- Licensed videos: Accessible to everyone\n' +
      '- Private videos: Only accessible to the owner when authenticated\n\n' +
      'Authentication is optional - provide a Bearer token to access private videos you own.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: VideoDto,
    description: 'Video details',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. Video is private and you are not the owner',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found or is not accessible',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<VideoDto> {
    try {
      const user = request.user || null;

      // Get video using the existing getBy method which includes relations
      const video = await this.provider.getBy({ id });

      // Apply privacy controls
      const canAccess = this.checkVideoAccess(video, user);
      if (!canAccess) {
        throw new ForbiddenException(
          'This video is private and you are not authorized to view it',
        );
      }

      return await this.responseMapper.map(video, user?.id || null);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      if (error instanceof NotFoundException) {
        throw error;
      }
      // Log the actual error for debugging
      console.error('Error in video get endpoint:', error);
      throw new NotFoundException('Video not found or not accessible');
    }
  }

  /**
   * Check if a user can access a video based on privacy settings
   */
  private checkVideoAccess(video: any, user: UserEntity | null): boolean {
    // Public and licensed videos are accessible to everyone
    if (
      video.privacy === PrivacyEnum.PUBLIC ||
      video.privacy === PrivacyEnum.LICENSED
    ) {
      return true;
    }

    // Private videos are only accessible to the owner
    if (video.privacy === PrivacyEnum.PRIVATE) {
      return user !== null && user.id === video.userId;
    }

    // Unknown privacy setting - deny access
    return false;
  }

  @Get(':id/related-videos')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'video_related_list',
    summary: 'List related videos for a video',
    description:
      'Retrieves a paginated list of videos related to the specified video for the authenticated user.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the video\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number\n' +
      '- limit: Number of related videos per page\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: Sort order ("asc" or "desc")\n' +
      '- privacy: Filter by privacy\n' +
      '- isNsfw: Filter by NSFW content\n',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video',
    type: 'string',
    format: 'uuid',
  })
  @ApiQuery({
    type: VideoSearchRequest,
    description: 'Query parameters for searching and paginating related videos',
  })
  @ApiOkResponse({
    type: [VideoDto],
    description: 'Paginated list of related videos',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async related(
    @Request() request,
    @Query() query: VideoSearchRequest,
    @Res() res: Response,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = {
      ...inputFilters,
      userId: request.user.id,
    };

    const entities = await this.provider.findRelatedVideos(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
      id,
      request.user.id,
      id,
    );

    const totalCount = await this.provider.countRelatedVideos(
      filters,
      id,
      request.user.id,
      id,
    );

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }
}
