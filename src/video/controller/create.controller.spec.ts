import { Test, TestingModule } from '@nestjs/testing';
import { CreateController } from './create.controller';
import { VideoRequestManager } from '../service/request-manager';
import { VideoResponseMapper } from '../service/response-mapper';
import { VideoRequest } from '../dto/video.request';
import { VideoEntity } from '../entity/video.entity';
import { VideoDto } from '../dto/video.dto';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';

describe('CreateController - Video Creation Bug Fix', () => {
  let controller: CreateController;
  let requestManager: jest.Mocked<VideoRequestManager>;
  let responseMapper: jest.Mocked<VideoResponseMapper>;

  const mockUser = {
    id: 'user-1',
    hidePrompt: false,
  };

  const mockRequest = {
    user: mockUser,
  };

  beforeEach(async () => {
    const mockRequestManager = {
      create: jest.fn(),
    };

    const mockResponseMapper = {
      map: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CreateController],
      providers: [
        {
          provide: VideoRequestManager,
          useValue: mockRequestManager,
        },
        {
          provide: VideoResponseMapper,
          useValue: mockResponseMapper,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<CreateController>(CreateController);
    requestManager = module.get(VideoRequestManager);
    responseMapper = module.get(VideoResponseMapper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create video successfully with imageUrl (bug fix verification)', async () => {
      // Arrange - This is the scenario that was failing before the fix
      const requestBody: VideoRequest = {
        imageUrl: 'https://example.com/test-image.jpg',
        width: 1024,
        height: 768,
        prompt: 'Create an animated video from this image',
      };

      const mockVideoEntity = new VideoEntity();
      mockVideoEntity.id = 'video-1';
      mockVideoEntity.inputImageUrl = requestBody.imageUrl;

      const mockVideoDto: VideoDto = {
        id: 'video-1',
        imageUrl: requestBody.imageUrl,
        prompt: requestBody.prompt,
      } as VideoDto;

      requestManager.create.mockResolvedValue(mockVideoEntity);
      responseMapper.map.mockResolvedValue(mockVideoDto);

      // Act
      const result = await controller.create(requestBody, mockRequest);

      // Assert
      expect(result).toEqual(mockVideoDto);
      expect(requestManager.create).toHaveBeenCalledWith(requestBody, mockUser);
      expect(responseMapper.map).toHaveBeenCalledWith(mockVideoEntity);
    });

    it('should create video successfully with originalImageCompletionId (regression test)', async () => {
      // Arrange - Ensure existing functionality still works
      const requestBody: VideoRequest = {
        originalImageCompletionId: 'image-completion-1',
        prompt: 'Create an animated video from this image completion',
      };

      const mockVideoEntity = new VideoEntity();
      mockVideoEntity.id = 'video-2';
      mockVideoEntity.originalImageCompletionId =
        requestBody.originalImageCompletionId;

      const mockVideoDto: VideoDto = {
        id: 'video-2',
        originalImageCompletionId: requestBody.originalImageCompletionId,
        prompt: requestBody.prompt,
      } as VideoDto;

      requestManager.create.mockResolvedValue(mockVideoEntity);
      responseMapper.map.mockResolvedValue(mockVideoDto);

      // Act
      const result = await controller.create(requestBody, mockRequest);

      // Assert
      expect(result).toEqual(mockVideoDto);
      expect(requestManager.create).toHaveBeenCalledWith(requestBody, mockUser);
      expect(responseMapper.map).toHaveBeenCalledWith(mockVideoEntity);
    });

    it('should handle errors from request manager properly', async () => {
      // Arrange
      const requestBody: VideoRequest = {
        imageUrl: 'https://example.com/invalid-image.jpg',
        prompt: 'This should fail',
      };

      const error = new Error('Some other validation error');
      requestManager.create.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.create(requestBody, mockRequest)).rejects.toThrow(
        error,
      );
      expect(requestManager.create).toHaveBeenCalledWith(requestBody, mockUser);
      expect(responseMapper.map).not.toHaveBeenCalled();
    });
  });
});
