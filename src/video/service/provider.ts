import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import {
  Brackets,
  FindOneOptions,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { PrivacyEnum, VideoEntity } from '../entity/video.entity';

@Injectable()
export class VideoProvider extends AbstractProvider<VideoEntity> {
  constructor(
    @InjectRepository(VideoEntity)
    protected readonly repository: Repository<VideoEntity>,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  createBaseQueryBuilder(): SelectQueryBuilder<VideoEntity> {
    return this.repository
      .createQueryBuilder('video')
      .innerJoinAndSelect('video.user', 'user')
      .leftJoinAndSelect('video.organization', 'organization')
      .leftJoinAndSelect(
        'video.originalImageCompletion',
        'originalImageCompletion',
      )
      .leftJoinAndSelect(
        'originalImageCompletion.user',
        'originalImageCompletionUser',
      )
      .leftJoinAndSelect('video.boards', 'boardVideos');
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
  ): Promise<VideoEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`video.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  async countBy(criteria: any): Promise<number> {
    const queryBuilder = this.prepareQueryBuilder(criteria);
    return await queryBuilder.getCount();
  }

  prepareQueryBuilder(
    criteria: any,
    includeRelations = true,
  ): SelectQueryBuilder<VideoEntity> {
    const where = { ...criteria };
    const queryBuilder = includeRelations
      ? this.createBaseQueryBuilder()
      : this.repository.createQueryBuilder('video');

    // Specific known filters
    if (where.userId) {
      queryBuilder.andWhere('video.userId = :userId', {
        userId: where.userId,
      });
      delete where.userId;
    }

    if (where.organizationId) {
      queryBuilder.andWhere('video.organizationId = :organizationId', {
        organizationId: where.organizationId,
      });
      delete where.organizationId;
    }

    if (where.status) {
      queryBuilder.andWhere('video.status = :status', {
        status: where.status,
      });
      delete where.status;
    }

    if (where.boardId) {
      queryBuilder.andWhere('boardVideos.boardId = :boardId', {
        boardId: where.boardId,
      });
      delete where.boardId;
    }

    if (where.hidePrompt !== undefined) {
      queryBuilder.andWhere('video.hidePrompt = :hidePrompt', {
        hidePrompt: where.hidePrompt,
      });
      delete where.hidePrompt;
    }

    if (where.privacy) {
      queryBuilder.andWhere('video.privacy = :privacy', {
        privacy: where.privacy,
      });
      delete where.privacy;
    }

    // STRICT PRIVACY FILTERING FOR FEEDS: Only allow public videos in feed contexts
    // This is a critical security requirement to prevent private video leakage
    if (where.feedContext === true) {
      queryBuilder.andWhere('video.privacy = :feedPrivacy', {
        feedPrivacy: 'public',
      });
      delete where.feedContext;
    }

    if (where.isNsfw !== undefined) {
      queryBuilder.andWhere('video.isNsfw = :isNsfw', {
        isNsfw: where.isNsfw,
      });
      delete where.isNsfw;
    }

    if (where.hideFromUserProfile !== undefined) {
      queryBuilder.andWhere(
        'video.hideFromUserProfile = :hideFromUserProfile',
        {
          hideFromUserProfile: where.hideFromUserProfile,
        },
      );
      delete where.hideFromUserProfile;
    }

    if (where.isHot !== undefined) {
      queryBuilder.andWhere('video.isHot = :isHot', {
        isHot: where.isHot,
      });
      delete where.isHot;
    }

    // Date window filters similar to Videos (day/week/month/year)
    const d = new Date();
    let hasDateWindow = false;
    if (where.day) {
      d.setDate(d.getDate() - 1);
      hasDateWindow = true;
      delete where.day;
    }
    if (where.week) {
      d.setDate(d.getDate() - 7);
      hasDateWindow = true;
      delete where.week;
    }
    if (where.month) {
      d.setMonth(d.getMonth() - 1);
      hasDateWindow = true;
      delete where.month;
    }
    if (where.year) {
      d.setFullYear(d.getFullYear() - 1);
      hasDateWindow = true;
      delete where.year;
    }
    if (hasDateWindow) {
      queryBuilder.andWhere('video.createdAt BETWEEN :start AND :end', {
        start: d.toISOString(),
        end: new Date().toISOString(),
      });
    }

    // Whitelist for generic equality filters on video columns only
    const allowedGenericKeys = new Set([
      'id',
      'width',
      'height',
      'resolution',
      'systemVersion',
      'storageBucket',
      'storagePath',
      'generationSeconds',
      'inputImageUrl',
      'prompt',
      'promptSystem',
      // NOTE: createdAt/updatedAt filters are handled via date windows above
    ]);

    Object.keys(where).forEach((key) => {
      if (allowedGenericKeys.has(key)) {
        queryBuilder.andWhere(`video.${key} = :${key}`, {
          [key]: where[key],
        });
      }
      // Ignore unsupported keys silently to prevent query errors
    });

    return queryBuilder;
  }

  async countRelatedVideos(
    criteria: any,
    id: string,
    userId: string,
    originalId: string,
  ) {
    const queryBuilder = this.prepareQueryBuilderRelatedVideos(
      criteria,
      id,
      userId,
      originalId,
    );
    return queryBuilder.getCount();
  }

  async findRelatedVideos(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
    id: string,
    userId: string,
    originalId: string,
  ): Promise<VideoEntity[]> {
    const queryBuilder = this.prepareQueryBuilderRelatedVideos(
      criteria,
      id,
      userId,
      originalId,
    );

    queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`video.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  prepareQueryBuilderRelatedVideos(
    criteria: any,
    id: string,
    userId: string,
    originalId: string,
  ) {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    queryBuilder.where(
      new Brackets((queryBrackets) => {
        queryBrackets
          .where('video.id = :id', { id: id })
          .orWhere('video.regeneratedFromId = :regeneratedFromId', {
            regeneratedFromId: id,
          })
          .orWhere('video.originalVideoId = :originalVideoId', {
            originalVideoId: id,
          });
      }),
    );
    queryBuilder.andWhere('video.id <> :originalId', {
      originalId: originalId,
    });
    queryBuilder.andWhere('video.userId = :userId', {
      userId: userId,
    });
    queryBuilder.andWhere(
      new Brackets((queryBrackets) => {
        queryBrackets
          .where('video.privacy = :privacy', {
            privacy: PrivacyEnum.PUBLIC,
          })
          .orWhere('video.userId = :userId', {
            userId: userId,
          });
      }),
    );
    Object.keys(where).forEach((key) => {
      queryBuilder.andWhere(`video.${key} = :${key}`, {
        [key]: where[key],
      });
    });

    return queryBuilder;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<VideoEntity> {
    return {
      where: criteria,
      relations: {
        user: true,
        organization: true,
        originalImageCompletion: {
          user: true,
        },
      },
    };
  }

  /**
   * Find video by ID with specific relations
   */
  async findByIdWithRelations(
    id: string,
    relations: string[] = [],
  ): Promise<VideoEntity | null> {
    try {
      const relationOptions: any = {};

      // Build relations object based on requested relations
      relations.forEach((relation) => {
        if (relation === 'user') {
          relationOptions.user = true;
        } else if (relation === 'organization') {
          relationOptions.organization = true;
        } else if (relation === 'originalImageCompletion') {
          relationOptions.originalImageCompletion = {
            user: true,
          };
        }
      });

      return await this.repository.findOne({
        where: { id },
        relations: relationOptions,
      });
    } catch (error) {
      this.logger.error('Failed to find video by ID with relations', {
        id,
        relations,
        error: error.message,
      });
      throw error;
    }
  }
}
