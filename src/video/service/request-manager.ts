import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { VideoManager } from './manager';
import { VideoRequest } from '../dto/video.request';
import { Logger } from 'nestjs-pino';
import { StatusEnum, VideoEntity } from '../entity/video.entity';
import { UserEntity } from 'src/user/entity/user.entity';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { VideoInternalRequest } from '../dto/video.internal-request';
import { VideoPromptPrivacyRequest } from '../dto/video.prompt-privacy-request';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { VideoPrivacyRequest } from '../dto/video-privacy.request';
import { PaymentRequiredException } from 'src/subscription/exception/payment-required.exception';

@Injectable()
export class VideoRequestManager {
  constructor(
    private manager: VideoManager,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
    private readonly imageCompletionProvider: ImageCompletionProvider,
  ) {}

  async interrupt(entity: VideoEntity) {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Video is not being generated');
    }

    entity.status = StatusEnum.INTERRUPTED;

    try {
      await this.manager.save(entity);
    } catch (e) {
      this.logger.error('Error interrupting video generation', {
        video: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async updatePrivacy(
    entity: VideoEntity,
    request: VideoPrivacyRequest,
  ): Promise<VideoEntity> {
    if (entity.status != StatusEnum.READY) {
      throw new BadRequestException('Image not ready for publication');
    }

    if (entity.isNsfw) {
      throw new BadRequestException('Image publication not allowed');
    }

    entity.privacy = request.privacy;

    return await this.manager.update(entity);
  }

  async create(request: VideoRequest, user: UserEntity): Promise<VideoEntity> {
    if (!request.originalImageCompletionId && !request.imageUrl) {
      throw new BadRequestException(
        'Must provide either originalImageCompletionId or imageUrl',
      );
    }

    // Note: Width and height are no longer required when using imageUrl
    // The system can handle image URLs without explicit dimensions

    const entity = new VideoEntity();
    entity.userId = user.id;
    entity.user = user;

    // Only fetch ImageCompletionEntity if originalImageCompletionId is provided
    if (request.originalImageCompletionId) {
      try {
        entity.originalImageCompletion = await this.imageCompletionProvider.get(
          request.originalImageCompletionId,
        );
        entity.originalImageCompletionId = request.originalImageCompletionId;
      } catch (error) {
        this.logger.error('Failed to fetch ImageCompletionEntity', {
          originalImageCompletionId: request.originalImageCompletionId,
          error: error.message,
        });
        throw new BadRequestException(
          `Invalid originalImageCompletionId: ${request.originalImageCompletionId}`,
        );
      }
    }

    entity.inputImageUrl = request.imageUrl;
    entity.prompt = request.prompt;
    entity.webhookUrl = request.webhookUrl;
    entity.width = request.width;
    entity.height = request.height;
    entity.resolution = request.resolution;

    entity.settings = request.settings;

    // Set default value for imageCompletionsCount to satisfy database constraint
    entity.imageCompletionsCount = 0;

    // Set hidePrompt from request or user preference
    if (request.hidePrompt !== undefined) {
      entity.hidePrompt = request.hidePrompt;
    } else if (user.hidePrompt) {
      entity.hidePrompt = user.hidePrompt;
    }

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }
      entity.organizationId = request.organizationId;
    }

    return await this.manager.create(entity);
  }

  mapInternalRequestData(
    entity: VideoEntity,
    request: VideoInternalRequest,
  ): void {
    entity.status = request.status ?? entity.status;
    entity.storageBucket = request.storageBucket ?? entity.storageBucket;
    entity.storagePath = request.storagePath ?? entity.storagePath;
    entity.generationSeconds =
      request.generationSeconds ?? entity.generationSeconds;
    entity.webhookUrl = request.webhookUrl ?? entity.webhookUrl;
    entity.hidePrompt = request.hidePrompt ?? entity.hidePrompt;
    entity.width = request.width ?? entity.width;
    entity.height = request.height ?? entity.height;
    entity.resolution = request.resolution ?? entity.resolution;
    entity.videoPaths = request.videoPaths ?? entity.videoPaths;

    // Handle progress updates with validation
    if (request.progress !== undefined) {
      this.validateProgressUpdate(entity, request.progress);
      entity.progress = request.progress;
    }
  }

  private validateProgressUpdate(entity: VideoEntity, progress: number): void {
    // Progress can only be updated when status is GENERATING
    if (
      entity.status !== StatusEnum.GENERATING &&
      progress > 0 &&
      progress < 100
    ) {
      throw new BadRequestException(
        'Progress can only be updated when video status is GENERATING',
      );
    }

    // Ensure progress is within valid range (additional validation beyond DTO)
    if (progress < 0 || progress > 100) {
      throw new BadRequestException('Progress must be between 0 and 100');
    }
  }

  async updateInternal(
    entity: VideoEntity,
    request: VideoInternalRequest,
  ): Promise<VideoEntity> {
    this.mapInternalRequestData(entity, request);

    try {
      return await this.manager.update(entity);
    } catch (e) {
      if ('user_credit_balance.not_found' === e.message) {
        throw new PaymentRequiredException();
      }
      this.logger.error('Error updating video', {
        video: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async updatePromptPrivacy(
    entity: VideoEntity,
    request: VideoPromptPrivacyRequest,
  ): Promise<VideoEntity> {
    entity.hidePrompt = request.hidePrompt;
    return await this.manager.update(entity);
  }
}
