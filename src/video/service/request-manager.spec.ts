import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { VideoRequestManager } from './request-manager';
import { VideoManager } from './manager';
import { VideoEntity, StatusEnum } from '../entity/video.entity';
import { VideoInternalRequest } from '../dto/video.internal-request';
import { VideoRequest } from '../dto/video.request';
import { UserEntity } from 'src/user/entity/user.entity';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { Logger } from 'nestjs-pino';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';

describe('VideoRequestManager', () => {
  let requestManager: VideoRequestManager;
  let videoManager: jest.Mocked<VideoManager>;
  let imageCompletionProvider: jest.Mocked<ImageCompletionProvider>;
  let logger: jest.Mocked<Logger>;

  const mockUser: UserEntity = {
    id: 'user-1',
    hidePrompt: false,
  } as UserEntity;

  const mockImageCompletion: ImageCompletionEntity = {
    id: 'image-1',
    prompt: 'Test image',
  } as ImageCompletionEntity;

  beforeEach(async () => {
    const mockVideoManager = {
      create: jest.fn(),
      update: jest.fn(),
    };

    const mockLogger = {
      error: jest.fn(),
    };

    const mockOrganizationUserProvider = {
      isMember: jest.fn(),
    };

    const mockImageCompletionProvider = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VideoRequestManager,
        {
          provide: VideoManager,
          useValue: mockVideoManager,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
        {
          provide: OrganizationUserProvider,
          useValue: mockOrganizationUserProvider,
        },
        {
          provide: ImageCompletionProvider,
          useValue: mockImageCompletionProvider,
        },
      ],
    }).compile();

    requestManager = module.get<VideoRequestManager>(VideoRequestManager);
    videoManager = module.get(VideoManager);
    imageCompletionProvider = module.get(ImageCompletionProvider);
    logger = module.get(Logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create - Video Creation with Image URLs', () => {
    it('should create video successfully with imageUrl and dimensions', async () => {
      // Arrange
      const request: VideoRequest = {
        imageUrl: 'https://example.com/image.jpg',
        width: 1024,
        height: 768,
        prompt: 'Test video creation',
      };

      const expectedEntity = new VideoEntity();
      videoManager.create.mockResolvedValue(expectedEntity);

      // Act
      const result = await requestManager.create(request, mockUser);

      // Assert
      expect(result).toBe(expectedEntity);
      expect(videoManager.create).toHaveBeenCalledWith(
        expect.objectContaining({
          inputImageUrl: 'https://example.com/image.jpg',
          width: 1024,
          height: 768,
          prompt: 'Test video creation',
          userId: mockUser.id,
          user: mockUser,
        }),
      );

      // Verify that originalImageCompletion fields are not set
      const calledEntity = videoManager.create.mock.calls[0][0];
      expect(calledEntity.originalImageCompletion).toBeUndefined();
      expect(calledEntity.originalImageCompletionId).toBeUndefined();
      expect(imageCompletionProvider.get).not.toHaveBeenCalled();
    });

    it('should create video successfully with originalImageCompletionId', async () => {
      // Arrange
      const request: VideoRequest = {
        originalImageCompletionId: 'image-1',
        prompt: 'Test video creation',
      };

      const expectedEntity = new VideoEntity();
      imageCompletionProvider.get.mockResolvedValue(mockImageCompletion);
      videoManager.create.mockResolvedValue(expectedEntity);

      // Act
      const result = await requestManager.create(request, mockUser);

      // Assert
      expect(result).toBe(expectedEntity);
      expect(imageCompletionProvider.get).toHaveBeenCalledWith('image-1');
      expect(videoManager.create).toHaveBeenCalledWith(
        expect.objectContaining({
          originalImageCompletion: mockImageCompletion,
          originalImageCompletionId: 'image-1',
          prompt: 'Test video creation',
          userId: mockUser.id,
          user: mockUser,
        }),
      );
    });

    it('should throw error when neither imageUrl nor originalImageCompletionId provided', async () => {
      // Arrange
      const request: VideoRequest = {
        prompt: 'Test video creation',
      };

      // Act & Assert
      await expect(requestManager.create(request, mockUser)).rejects.toThrow(
        BadRequestException,
      );
      await expect(requestManager.create(request, mockUser)).rejects.toThrow(
        'Must provide either originalImageCompletionId or imageUrl',
      );
    });

    it('should allow imageUrl without dimensions', async () => {
      // Arrange
      const request: VideoRequest = {
        imageUrl: 'https://example.com/image.jpg',
        prompt: 'Test video creation',
        // Width and height are no longer required
      };

      const mockVideoEntity = {
        id: 'video-1',
        inputImageUrl: 'https://example.com/image.jpg',
        prompt: 'Test video creation',
        userId: 'user-1',
      } as VideoEntity;

      videoManager.create = jest.fn().mockResolvedValue(mockVideoEntity);

      // Act
      const result = await requestManager.create(request, mockUser);

      // Assert
      expect(result).toBeDefined();
      expect(result.inputImageUrl).toBe('https://example.com/image.jpg');
      expect(result.prompt).toBe('Test video creation');
    });

    it('should throw error when originalImageCompletionId is invalid', async () => {
      // Arrange
      const request: VideoRequest = {
        originalImageCompletionId: 'invalid-id',
        prompt: 'Test video creation',
      };

      imageCompletionProvider.get.mockRejectedValue(
        new Error('Entity not found'),
      );

      // Act & Assert
      await expect(requestManager.create(request, mockUser)).rejects.toThrow(
        BadRequestException,
      );
      await expect(requestManager.create(request, mockUser)).rejects.toThrow(
        'Invalid originalImageCompletionId: invalid-id',
      );
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to fetch ImageCompletionEntity',
        expect.objectContaining({
          originalImageCompletionId: 'invalid-id',
          error: 'Entity not found',
        }),
      );
    });

    it('should allow both imageUrl and originalImageCompletionId (originalImageCompletionId takes precedence)', async () => {
      // Arrange
      const request: VideoRequest = {
        originalImageCompletionId: 'image-1',
        imageUrl: 'https://example.com/image.jpg',
        prompt: 'Test video creation',
      };

      const expectedEntity = new VideoEntity();
      imageCompletionProvider.get.mockResolvedValue(mockImageCompletion);
      videoManager.create.mockResolvedValue(expectedEntity);

      // Act
      const result = await requestManager.create(request, mockUser);

      // Assert
      expect(result).toBe(expectedEntity);
      expect(imageCompletionProvider.get).toHaveBeenCalledWith('image-1');
      expect(videoManager.create).toHaveBeenCalledWith(
        expect.objectContaining({
          originalImageCompletion: mockImageCompletion,
          originalImageCompletionId: 'image-1',
          inputImageUrl: 'https://example.com/image.jpg',
        }),
      );
    });
  });

  describe('updateInternal - Progress Updates', () => {
    it('should update progress successfully when status is GENERATING', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.GENERATING,
        progress: 0,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        progress: 50,
      };

      const updatedEntity = {
        ...entity,
        progress: 50,
        get description() {
          return this.prompt;
        },
        get thumbnailUrl() {
          return undefined;
        },
      };
      videoManager.update.mockResolvedValue(updatedEntity);

      // Act
      const result = await requestManager.updateInternal(entity, request);

      // Assert
      expect(entity.progress).toBe(50);
      expect(videoManager.update).toHaveBeenCalledWith(entity);
      expect(result).toEqual(updatedEntity);
    });

    it('should allow progress 0 and 100 regardless of status', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.NEW,
        progress: 0,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        progress: 100,
      };

      const updatedEntity = {
        ...entity,
        progress: 100,
        get description() {
          return this.prompt;
        },
        get thumbnailUrl() {
          return undefined;
        },
      };
      videoManager.update.mockResolvedValue(updatedEntity);

      // Act
      const result = await requestManager.updateInternal(entity, request);

      // Assert
      expect(entity.progress).toBe(100);
      expect(videoManager.update).toHaveBeenCalledWith(entity);
    });

    it('should reject progress updates when status is not GENERATING', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.READY,
        progress: 100,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        progress: 50,
      };

      // Act & Assert
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow(
        'Progress can only be updated when video status is GENERATING',
      );
    });

    it('should reject progress values below 0', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.GENERATING,
        progress: 0,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        progress: -1,
      };

      // Act & Assert
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow('Progress must be between 0 and 100');
    });

    it('should reject progress values above 100', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.GENERATING,
        progress: 0,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        progress: 101,
      };

      // Act & Assert
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow(BadRequestException);
      await expect(
        requestManager.updateInternal(entity, request),
      ).rejects.toThrow('Progress must be between 0 and 100');
    });

    it('should not update progress when not provided in request', async () => {
      // Arrange
      const entity: VideoEntity = {
        id: 'video-1',
        status: StatusEnum.GENERATING,
        progress: 25,
      } as VideoEntity;

      const request: VideoInternalRequest = {
        status: StatusEnum.READY,
      };

      const updatedEntity = {
        ...entity,
        status: StatusEnum.READY,
        get description() {
          return this.prompt;
        },
        get thumbnailUrl() {
          return undefined;
        },
      };
      videoManager.update.mockResolvedValue(updatedEntity);

      // Act
      await requestManager.updateInternal(entity, request);

      // Assert
      expect(entity.progress).toBe(25); // Should remain unchanged
      expect(entity.status).toBe(StatusEnum.READY);
    });
  });
});
