import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ImageCompletionResponseMapper } from '../../image-completion/service/response-mapper';
import { VideoDto } from '../dto/video.dto';
import { VideoEntity } from '../entity/video.entity';
import { UserResponseMapper } from 'src/user/service/response-mapper';

@Injectable()
export class VideoResponseMapper {
  private cdnHost: string;

  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => ImageCompletionResponseMapper))
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly userResponseMapper: UserResponseMapper,
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }

  async mapMultiple(entities: VideoEntity[]): Promise<VideoDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity)));
  }

  async mapMultipleInternal(entities: VideoEntity[]): Promise<VideoDto[]> {
    return Promise.all(entities.map((entity) => this.mapInternal(entity)));
  }

  async map(entity: VideoEntity, userId: string = null): Promise<VideoDto> {
    const dto = new VideoDto();

    dto.id = entity.id;

    // Map original image completion (the image being animated)
    if (entity.originalImageCompletion) {
      dto.originalImageCompletion =
        await this.imageCompletionResponseMapper.map(
          entity.originalImageCompletion,
          true,
          true,
          userId,
        );
      dto.originalImageCompletionId = entity.originalImageCompletionId;
    }

    dto.imageUrl = entity.inputImageUrl;

    // Map video operation metadata
    if (!entity.hidePrompt) {
      dto.prompt = entity.prompt;
      dto.promptSystem = entity.promptSystem;
    }

    dto.width = entity.width;
    dto.height = entity.height;
    dto.resolution = entity.resolution;
    dto.status = entity.status;
    dto.progress = entity.progress;
    dto.systemVersion = entity.systemVersion;
    dto.generationSeconds = entity.generationSeconds;
    dto.settings = entity.settings;
    dto.webhookUrl = entity.webhookUrl;
    dto.hidePrompt = entity.hidePrompt;
    dto.createdAt = entity.createdAt;
    dto.privacy = entity.privacy;
    dto.user = this.userResponseMapper.mapPublic(entity.user);

    // Video-specific paths and storage info
    if (entity.videoPaths) {
      dto.videoVersions = this.generateVideoVersions(entity.videoPaths[0]);
    }
    dto.storageBucket = entity.storageBucket;
    dto.storagePath = entity.storagePath;

    return dto;
  }

  generateThumbnailUrl(video: VideoEntity): string {
    const videoVersions = this.generateVideoVersions(video.videoPaths[0]);

    return videoVersions['240p'];
  }

  generateVideoVersions(originalVideo: string): {
    [key: string]: string;
  } {
    const originalVideoUrl = this.cdnHost + '/videos/' + originalVideo;

    const versions = {
      original: originalVideoUrl,
      '240p': this.composeUrlWithResolution(originalVideoUrl, '240p'),
    };

    return versions;
  }

  private composeUrlWithResolution(
    originalUrl: string,
    resolution: string,
  ): string {
    const [base, extension] = originalUrl.split(/\.(?=[^\.]+$)/);
    return `${base}_${resolution}.${extension}`;
  }

  async mapInternal(entity: VideoEntity): Promise<VideoDto> {
    const dto = await this.map(entity);

    // Add internal-only fields
    dto.generationSeconds = entity.generationSeconds;

    return dto;
  }
}
