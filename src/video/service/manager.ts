import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrivacyEnum, VideoEntity } from '../entity/video.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AssetManager } from './asset.manager';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { TransactionManager } from 'src/subscription/service/transaction.manager';
import { TransactionTypeEnum } from 'src/subscription/entity/transaction-type.enum';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { VideoResponseMapper } from './response-mapper';
import { Logger } from 'nestjs-pino';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { VideoPublishedEvent } from '../event/video-published.event';

@Injectable()
export class VideoManager {
  constructor(
    @InjectRepository(VideoEntity)
    private repository: Repository<VideoEntity>,
    private assetManager: AssetManager,
    private configService: ConfigService,
    private transactionManager: TransactionManager,
    @Inject('SQS')
    private sqs: SQSClient,
    private responseMapper: VideoResponseMapper,
    private logger: Logger,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(entity: VideoEntity): Promise<VideoEntity> {
    entity.id = uuidv4();
    entity.promptSystem = await this.generatePromptSystem(entity);
    entity.generationSeconds = 1;
    entity.privacy = PrivacyEnum.PRIVATE;

    this.assetManager.generateStoragePath(entity);

    await this.save(entity);

    if (!entity.user.isBot) {
      await this.registerTransaction(entity);
    }

    await this.writeToQueue(entity);

    return entity;
  }

  async update(entity: VideoEntity): Promise<VideoEntity> {
    // Check if video is being published (privacy changing to PUBLIC)
    const wasPrivate = entity.publishedAt === null;
    const isBecomingPublic = entity.privacy === PrivacyEnum.PUBLIC;

    if (wasPrivate && isBecomingPublic) {
      entity.publishedAt = new Date();
      await this.save(entity);

      // Emit video published event for feed updates
      this.eventEmitter.emit(
        'video.published',
        new VideoPublishedEvent({
          id: entity.id,
          userId: entity.userId,
          publishedAt: entity.publishedAt,
        }),
      );

      this.logger.log('Video published', {
        videoId: entity.id,
        userId: entity.userId,
        publishedAt: entity.publishedAt,
      });
    } else if (
      entity.publishedAt !== null &&
      entity.privacy === PrivacyEnum.PRIVATE
    ) {
      // Video is being unpublished
      entity.publishedAt = null;
      await this.save(entity);

      // TODO: Emit video unpublished event if needed for feed cleanup
      this.logger.log('Video unpublished', {
        videoId: entity.id,
        userId: entity.userId,
      });
    } else {
      // Regular update without privacy change
      await this.save(entity);
    }

    if (!entity.user.isBot) {
      await this.registerTransaction(entity);
    }

    return entity;
  }

  async registerTransaction(entity: VideoEntity) {
    const billedSeconds = entity.generationSeconds;
    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      billedSeconds,
      CreditTypeEnum.IMAGE,
      entity.id,
      true,
      entity.organizationId ? null : entity.userId,
      entity.organizationId,
    );
  }

  async writeToQueue(entity: VideoEntity) {
    const queueUrl = this.configService.get<string>('VIDEO_SQS_QUEUE_URL');

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(
        await this.responseMapper.mapInternal(entity),
      ),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async save(entity: VideoEntity) {
    if (!entity.storagePath) {
      this.assetManager.generateStoragePath(entity);
    }
    await this.repository.save(entity);
  }

  async generatePromptSystem(entity: VideoEntity): Promise<string> {
    let promptSystem = entity.prompt || '';

    // Add all settings properties to prompt system
    if (entity.settings && typeof entity.settings === 'object') {
      Object.entries(entity.settings).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          // Convert camelCase to readable format
          const readableKey = key.replace(/([A-Z])/g, ' $1').toLowerCase();
          promptSystem += ` ${readableKey}: ${value},`;
        }
      });
    }

    // Add resolution-specific animation prompts
    if (entity.resolution) {
      promptSystem += ` ${entity.resolution}p resolution,`;
    }

    // Add dimension-specific prompts for animation
    promptSystem += ` ${entity.width}x${entity.height} aspect ratio`;

    promptSystem += promptSystem.endsWith(',') ? '' : ',';

    return promptSystem;
  }

  async delete(entity: VideoEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }

  async interrupt(entity: VideoEntity): Promise<void> {
    if (entity.status != 'generating' && entity.status != 'new') {
      throw new BadRequestException('Video is not being generated');
    }

    entity.status = 'interrupted';
    await this.save(entity);
  }

  /**
   * Increment the like count for a video
   */
  async incrementLikeCount(videoId: string): Promise<void> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .update(VideoEntity)
        .set({ likes: () => 'likes + 1' })
        .where('id = :id', { id: videoId })
        .execute();

      if (result.affected === 0) {
        throw new NotFoundException('Video not found for like count update');
      }

      this.logger.log('Incremented video like count', { videoId });
    } catch (error) {
      this.logger.error('Failed to increment video like count', {
        videoId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Decrement the like count for a video
   */
  async decrementLikeCount(videoId: string): Promise<void> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .update(VideoEntity)
        .set({ likes: () => 'GREATEST(0, likes - 1)' })
        .where('id = :id', { id: videoId })
        .execute();

      if (result.affected === 0) {
        throw new NotFoundException('Video not found for like count update');
      }

      this.logger.log('Decremented video like count', { videoId });
    } catch (error) {
      this.logger.error('Failed to decrement video like count', {
        videoId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Increment the comment count for a video
   */
  async incrementCommentCount(videoId: string): Promise<void> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .update(VideoEntity)
        .set({ comments: () => 'comments + 1' })
        .where('id = :id', { id: videoId })
        .execute();

      if (result.affected === 0) {
        throw new NotFoundException('Video not found for comment count update');
      }

      this.logger.log('Incremented video comment count', { videoId });
    } catch (error) {
      this.logger.error('Failed to increment video comment count', {
        videoId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Decrement the comment count for a video
   */
  async decrementCommentCount(videoId: string): Promise<void> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .update(VideoEntity)
        .set({ comments: () => 'GREATEST(0, comments - 1)' })
        .where('id = :id', { id: videoId })
        .execute();

      if (result.affected === 0) {
        throw new NotFoundException('Video not found for comment count update');
      }

      this.logger.log('Decremented video comment count', { videoId });
    } catch (error) {
      this.logger.error('Failed to decrement video comment count', {
        videoId,
        error: error.message,
      });
      throw error;
    }
  }
}
