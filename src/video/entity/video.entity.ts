import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { BoardVideoEntity } from '../../board/entity/board-video.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum StatusEnum {
  NEW = 'new',
  GENERATING = 'generating',
  READY = 'ready',
  SAVED = 'saved',
  FAILED = 'failed',
  INTERRUPTED = 'interrupted',
}

export enum PrivacyEnum {
  PUBLIC = 'public',
  PRIVATE = 'private',
  LICENSED = 'licensed',
}

@Entity('video')
export class VideoEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_video_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_video_original_image_completion_id')
  originalImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { eager: true, nullable: true })
  originalImageCompletion: ImageCompletionEntity;

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({ type: 'text', nullable: true })
  promptSystem?: string;

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ nullable: false, default: 480 })
  resolution?: number;

  @Column({ nullable: false, default: 480 })
  width: number;

  @Column({ nullable: false, default: 480 })
  height: number;

  @Column({ type: 'json', nullable: true })
  settings?: any;

  @Column({ type: 'json', nullable: true, default: {} })
  generationSettings?: any;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @Column({ type: 'text', nullable: true })
  inputImageUrl?: string;

  @Column({ nullable: false, default: StatusEnum.NEW })
  status: string;

  @Column({ nullable: false, default: 0 })
  progress: number;

  @Column({ nullable: false, default: 1 })
  systemVersion?: number;

  @Column({ nullable: false, default: 0 })
  generationSeconds: number;

  @Column({ type: 'json', nullable: true })
  videoPaths?: any;

  @Column({ type: 'int', nullable: false, default: 0 })
  imageCompletionsCount: number;

  @Column({ nullable: false, default: 0 })
  comments: number;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @Column({ nullable: false, default: 0 })
  regenerations: number;

  @Column({ nullable: false, default: 0 })
  reports: number;

  @Column({
    type: 'enum',
    enum: PrivacyEnum,
    default: PrivacyEnum.PUBLIC,
    nullable: false,
  })
  privacy: PrivacyEnum;

  @Column({ default: false })
  isNsfw: boolean;

  @Column({ default: false })
  hidePrompt: boolean;

  @Column({ default: false })
  hideFromUserProfile: boolean;

  @Column({ default: false })
  isUnsafe: boolean;

  @Column({ default: false })
  @Index()
  isHot: boolean;

  @Column({ default: false })
  isActive: boolean;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  blockedAt?: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  @Index('idx_video_deleted_at')
  deletedAt?: Date;

  @CreateDateColumn()
  @Index('idx_video_created_at')
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => BoardVideoEntity, (boardVideo) => boardVideo.video, {
    eager: false,
  })
  boards: BoardVideoEntity[];

  // New columns added by feed and search migrations
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'text', array: true, nullable: true })
  searchTags?: string[];

  // Virtual properties for compatibility
  get description(): string | undefined {
    return this.prompt;
  }

  get thumbnailUrl(): string | undefined {
    if (this.videoPaths && this.videoPaths.thumbnail) {
      return this.videoPaths.thumbnail;
    }
    return this.originalImageCompletion?.previewImage;
  }
}
