import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  Max,
} from 'class-validator';
import { StatusEnum } from '../entity/video.entity';

export class VideoInternalRequest {
  @IsOptional()
  @IsEnum(StatusEnum)
  @ApiProperty({
    description: 'Video status',
    enum: StatusEnum,
    required: false,
  })
  status?: StatusEnum;

  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Progress must be at least 0' })
  @Max(100, { message: 'Progress must be at most 100' })
  @ApiProperty({
    description: 'Generation progress (0-100)',
    required: false,
    minimum: 0,
    maximum: 100,
    example: 50,
  })
  progress?: number;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Storage bucket name', required: false })
  storageBucket?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Storage path', required: false })
  storagePath?: string;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Hide prompt flag', required: false })
  hidePrompt?: boolean;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Width of the video', required: false })
  width?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Height of the video', required: false })
  height?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Resolution of the video', required: false })
  resolution?: number;

  @IsOptional()
  @IsArray()
  @ApiProperty({ description: 'Video paths', required: false })
  videoPaths?: any;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Generation time in seconds', required: false })
  generationSeconds?: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'NSFW content flag', required: false })
  isNsfw?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Hot content flag', required: false })
  isHot?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Unsafe content flag', required: false })
  isUnsafe?: boolean;

  @IsOptional()
  @IsString()
  @ApiProperty({
    description: 'Webhook URL for notifications',
    required: false,
  })
  webhookUrl?: string;
}
