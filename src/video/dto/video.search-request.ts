import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { BaseSearchRequest } from 'src/core/dto/base.search-request';
import { StatusEnum } from '../entity/video.entity';

export class VideoSearchRequest extends BaseSearchRequest {
  @IsOptional()
  @IsUUID()
  @ApiProperty({ description: 'Filter by user ID', required: false })
  userId?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Filter by username', required: false })
  username?: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({ description: 'Filter by organization ID', required: false })
  organizationId?: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty({
    description: 'Filter by original image completion ID',
    required: false,
  })
  originalImageCompletionId?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Filter by prompt text', required: false })
  prompt?: string;

  @IsOptional()
  @IsEnum(StatusEnum)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({
    description: 'Filter by video status',
    enum: StatusEnum,
    required: false,
  })
  status?: StatusEnum;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Filter by width', required: false })
  width?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Filter by height', required: false })
  height?: number;

  @IsOptional()
  @IsNumber()
  @ApiProperty({ description: 'Filter by resolution', required: false })
  resolution?: number;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Filter by NSFW content', required: false })
  isNsfw?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Filter by hot content', required: false })
  isHot?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Filter by unsafe content', required: false })
  isUnsafe?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({ description: 'Filter by hidden prompts', required: false })
  hidePrompt?: boolean;
}
