import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
} from 'class-validator';

export class VideoRequest {
  @ApiProperty({ description: 'Original image completion ID', required: false })
  @IsOptional()
  @IsUUID()
  originalImageCompletionId?: string;

  @ApiProperty({ description: 'Image URL', required: false })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({ description: 'Width of the edited image', required: false })
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty({ description: 'Height of the edited image', required: false })
  @IsInt()
  @IsOptional()
  height?: number;

  @ApiProperty({
    description: 'Resolution of the edited image',
    required: false,
  })
  @IsInt()
  @IsOptional()
  resolution?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({ default: false })
  @IsOptional()
  @IsBoolean()
  hidePrompt?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  settings?: any;
}
