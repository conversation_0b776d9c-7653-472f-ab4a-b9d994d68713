import { ApiProperty } from '@nestjs/swagger';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { ModelDto } from 'src/model/dto/model.dto';
import { PublicUserDto } from 'src/user/dto/public.user.dto';

export class VideoDto {
  @ApiProperty({ description: 'Unique identifier for the video animation' })
  id: string;

  @ApiProperty({ description: 'User who created the video' })
  user: PublicUserDto;

  @ApiProperty({ description: 'User ID of the video creator' })
  userId?: string;

  @ApiProperty({ description: 'ID of the video this was regenerated from' })
  regeneratedFromId?: string;

  @ApiProperty({
    description: 'Original image completion being animated',
    type: () => ImageCompletionDto,
  })
  originalImageCompletion: ImageCompletionDto;

  @ApiProperty({ description: 'Original image completion ID' })
  originalImageCompletionId?: string;

  @ApiProperty({ description: 'Video animation prompt' })
  prompt?: string;

  @ApiProperty({ description: 'System prompt for video generation' })
  promptSystem?: string;

  @ApiProperty({ description: 'Input image URL' })
  imageUrl?: string;

  @ApiProperty({ description: 'Video width in pixels' })
  width: number;

  @ApiProperty({ description: 'Video height in pixels' })
  height: number;

  @ApiProperty({ description: 'Video resolution' })
  resolution?: number;

  @ApiProperty({ description: 'Video generation settings' })
  settings?: any;

  @ApiProperty({ description: 'Generation settings used for the video' })
  generationSettings?: any;

  @ApiProperty({
    description: 'Status of the video animation process',
    required: false,
  })
  status: string;

  @ApiProperty({ description: 'Detailed status information' })
  statusDetail?: string;

  @ApiProperty({ description: 'Generation progress percentage (0-100)' })
  progress?: number;

  @ApiProperty({ description: 'Preview image URL for the video' })
  previewImage?: string;

  @ApiProperty({ description: 'Processing queue type' })
  queue?: string;

  @ApiProperty({ description: 'System version used for generation' })
  systemVersion?: number;

  @ApiProperty({ description: 'Generation time in seconds' })
  generationSeconds: number;

  @ApiProperty({ description: 'Generated video file versions' })
  videoVersions?: { [key: string]: any };

  @ApiProperty({ description: 'Generated image file versions' })
  imageVersions?: { [key: string]: any };

  @ApiProperty({ description: 'Storage bucket for video files' })
  storageBucket?: string;

  @ApiProperty({ description: 'Storage path for video files' })
  storagePath?: string;

  @ApiProperty({ description: 'Webhook URL for completion notification' })
  webhookUrl?: string;

  @ApiProperty({ description: 'Whether to hide the prompt from public view' })
  hidePrompt?: boolean;

  @ApiProperty({ description: 'Whether the video has a watermark' })
  hasWatermark?: boolean;

  @ApiProperty({ description: 'Number of likes for this video' })
  likes: number;

  @ApiProperty({ description: 'Number of comments for this video' })
  comments: number;

  @ApiProperty({ description: 'Number of regenerations/remixes of this video' })
  regenerations: number;

  @ApiProperty({ description: 'Number of reports for inappropriate content' })
  reports: number;

  @ApiProperty({ description: 'Privacy setting for the video' })
  privacy: string;

  @ApiProperty({ description: 'Whether the video contains NSFW content' })
  isNsfw: boolean;

  @ApiProperty({ description: 'Whether the video is flagged as trending/hot' })
  isHot: boolean;

  @ApiProperty({ description: 'Whether the current user has liked this video' })
  liked: boolean;

  @ApiProperty({
    description: 'Whether the video is flagged as unsafe content',
  })
  isUnsafe?: boolean;

  @ApiProperty({ description: 'Whether the video is actively available' })
  isActive: boolean;

  @ApiProperty({ description: 'Whether to hide the video from user profile' })
  hideFromUserProfile: boolean;

  @ApiProperty({
    description: 'Whether the current user has bookmarked this video',
  })
  isBookmarked?: boolean;

  @ApiProperty({ description: 'Username of the video creator' })
  username: string;

  @ApiProperty({ description: 'Whether the creator is a verified user' })
  isUserVerified: boolean;

  @ApiProperty({ description: 'When the video was published/made public' })
  publishedAt?: Date;

  @ApiProperty({ description: 'When the video was blocked (if applicable)' })
  blockedAt?: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({
    description: 'Models used for video generation',
    type: () => [ModelDto],
  })
  models?: ModelDto[];
}
