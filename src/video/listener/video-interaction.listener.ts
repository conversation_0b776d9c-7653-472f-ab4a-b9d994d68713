import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from 'nestjs-pino';
import { VideoManager } from '../service/manager';
import { VideoProvider } from '../service/provider';
import { Notifier } from 'src/notification/service/notifier';
import { VideoCommentedNotification } from '../notification/video-commented.notification';
import { UserProvider } from 'src/user/service/provider';
import { UserResponseMapper } from 'src/user/service/response-mapper';

@Injectable()
export class VideoInteractionListener {
  private static instances: Set<string> = new Set();
  private readonly instanceId: string;

  constructor(
    private readonly videoManager: VideoManager,
    private readonly logger: Logger,
    private readonly videoProvider: VideoProvider,
    private readonly notifier: Notifier,
    private readonly userProvider: UserProvider,
    private readonly userResponseMapper: UserResponseMapper,
  ) {
    this.instanceId = Math.random().toString(36).substring(7);
    VideoInteractionListener.instances.add(this.instanceId);
  }

  private isPrimaryInstance(): boolean {
    return VideoInteractionListener.instances.has(this.instanceId);
  }

  @OnEvent('entity.liked')
  async handleVideoLiked(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (!this.isPrimaryInstance() || payload.entityType !== 'video') {
      return; // Only handle video likes from primary instance
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.incrementLikeCount(payload.entityId);

      this.logger.log('Video like count incremented', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        userId: payload.userId,
        instanceId: this.instanceId,
      });
    } catch (error) {
      this.logger.error('Failed to increment video like count', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        error: error.message,
        instanceId: this.instanceId,
      });
    }
  }

  @OnEvent('entity.unliked')
  async handleVideoUnliked(payload: {
    likeId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (!this.isPrimaryInstance() || payload.entityType !== 'video') {
      return;
    }

    try {
      // Use VideoManager for business logic/data modification
      await this.videoManager.decrementLikeCount(payload.entityId);

      this.logger.log('Video like count decremented', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        userId: payload.userId,
        instanceId: this.instanceId,
      });
    } catch (error) {
      this.logger.error('Failed to decrement video like count', {
        videoId: payload.entityId,
        likeId: payload.likeId,
        error: error.message,
        instanceId: this.instanceId,
      });
    }
  }

  @OnEvent('entity.comment.created')
  async handleVideoCommented(payload: {
    commentId: string;
    entityType: string;
    entityId: string;
    userId: string;
    comment: string;
  }): Promise<void> {
    if (payload.entityType !== 'video') {
      return;
    }

    try {
      const entityInfo = await this.videoProvider.get(payload.entityId);
      const user = await this.userProvider.get(payload.userId);

      const notificationData = {
        id: entityInfo.id,
        thumbnail: entityInfo.thumbnailUrl,
        userId: entityInfo.userId,
        commentedById: payload.userId,
        commentedByUsername: user.username,
        commentedByThumbnail: this.userResponseMapper.mapProfilePicture(user),
        commentedAt: new Date(),
      };

      await this.notifier.dispatch(
        new VideoCommentedNotification(entityInfo.userId, notificationData),
      );

      // Handle comment count increment
      await this.videoManager.incrementCommentCount(payload.entityId);

      this.logger.log('Video comment event handled', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        userId: payload.userId,
        instanceId: this.instanceId,
      });
    } catch (error) {
      this.logger.error('Failed to handle video comment event', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        error: error.message,
        instanceId: this.instanceId,
      });
    }
  }

  @OnEvent('entity.comment.deleted')
  async handleVideoCommentDeleted(payload: {
    commentId: string;
    entityType: string;
    entityId: string;
    userId: string;
  }): Promise<void> {
    if (!this.isPrimaryInstance() || payload.entityType !== 'video') {
      return;
    }

    try {
      this.logger.debug('Video comment delete event received', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        userId: payload.userId,
        instanceId: this.instanceId,
      });
      // Nothing to do here - EntityCommentManager handles the count decrement
    } catch (error) {
      this.logger.error('Failed to handle video comment delete event', {
        videoId: payload.entityId,
        commentId: payload.commentId,
        error: error.message,
      });
    }
  }
}
