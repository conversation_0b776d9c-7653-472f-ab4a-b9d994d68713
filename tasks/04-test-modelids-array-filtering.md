# Task 04: Test ModelIds Array Filtering

## Priority
High

## Category
User Feed Privacy/ModelIds Filters (Bug #1)

## Description
Ensure that when modelIds array is provided, only content generated by those specific model UUIDs is returned. Create comprehensive tests to validate model-based filtering behavior in user feeds.

## Technical Details

### Files to Create/Modify
- `src/feed/service/feed.service.spec.ts` - Unit tests for model filtering
- `test/integration/feed/user-feed-models.spec.ts` - Integration tests
- Test data setup with multiple models and content

### Key Test Scenarios
- **Empty Array**: No filtering, return content from all models
- **Single Model**: Return content only from specified model
- **Multiple Models**: Return content from any of the specified models
- **Invalid UUIDs**: Proper validation and error handling
- **Non-existent Models**: Handle gracefully (empty results)

### ModelIds Parameter Structure
```typescript
// From UserFeedRequestDto
@IsArray()
@IsUUID('4', { each: true, message: 'Each model ID must be a valid UUID' })
modelIds?: string[];
```

## Implementation Steps

1. **Create Test Data Setup**
   - Create multiple test models with different UUIDs
   - Create test content associated with different models
   - Set up model-content relationships in test database

2. **Unit Tests for Model Filtering**
   - Test modelIds array processing in service methods
   - Mock database queries and verify IN clauses
   - Test UUID validation and error handling

3. **Integration Tests for API Endpoint**
   - Test GET /feed/user/{userIdentifier} with various modelIds arrays
   - Verify response content matches model filter expectations
   - Test combination with other filters (privacy + modelIds)

4. **Validation and Error Tests**
   - Test invalid UUID formats
   - Test malformed arrays
   - Verify proper error responses and status codes

## Testing Requirements

### Unit Test Cases
```typescript
describe('ModelIds Filter Logic', () => {
  it('should return all content when modelIds is empty/null')
  it('should filter by single model UUID')
  it('should filter by multiple model UUIDs')
  it('should handle non-existent model UUIDs gracefully')
  it('should validate UUID format in modelIds array')
  it('should combine with privacy filters correctly')
})
```

### Integration Test Cases
```typescript
describe('GET /feed/user/:id ModelIds', () => {
  it('should return content from specified models only')
  it('should return empty array for non-existent models')
  it('should return 400 for invalid UUID format')
  it('should work with single model in array')
  it('should work with multiple models in array')
  it('should combine with privacy filter correctly')
})
```

### Test Data Requirements
- Model A, B, C with different UUIDs
- Content items associated with each model
- Mixed content types (images, videos) per model
- Invalid UUID examples for validation testing

## Dependencies
- Task 02: Audit UserFeedService implementation
- Task 03: Test privacy filter logic (for combined filtering tests)

## Acceptance Criteria

1. **Model Filtering Works Correctly**
   - Empty/null modelIds returns content from all models
   - Populated modelIds array returns only content from specified models
   - Multiple modelIds work with OR logic (content from any specified model)

2. **UUID Validation Enforced**
   - Invalid UUID formats return 400 Bad Request
   - Proper error messages for validation failures
   - Array validation works for each element

3. **Edge Cases Handled**
   - Non-existent model UUIDs return empty results (not errors)
   - Empty arrays treated as "no filter"
   - Large arrays handled efficiently

4. **Combined Filtering Works**
   - ModelIds + privacy filters work together
   - Results respect both constraints simultaneously
   - Performance acceptable with multiple filters

## Test Implementation Examples

### Unit Test Structure
```typescript
it('should apply modelIds filter with IN clause', async () => {
  const request = { modelIds: ['uuid1', 'uuid2'] };
  const mockQueryBuilder = createMockQueryBuilder();
  
  await feedService.getUserFeed('user-id', null, request);
  
  expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
    'entity.modelId IN (:...modelIds)', 
    { modelIds: ['uuid1', 'uuid2'] }
  );
});
```

### Integration Test Structure
```typescript
it('should return content from specified models only', async () => {
  const modelIds = [testModel1.id, testModel2.id];
  
  const response = await request(app)
    .get(`/feed/user/owner-id?modelIds=${modelIds.join(',')}`)
    .expect(200);
    
  expect(response.body.items).toHaveLength(4); // 2 items per model
  response.body.items.forEach(item => {
    expect(modelIds).toContain(item.modelId);
  });
});
```

### Validation Test Structure
```typescript
it('should return 400 for invalid UUID in modelIds', async () => {
  const response = await request(app)
    .get('/feed/user/owner-id?modelIds=invalid-uuid,another-invalid')
    .expect(400);
    
  expect(response.body.message).toContain('Each model ID must be a valid UUID');
});
```
