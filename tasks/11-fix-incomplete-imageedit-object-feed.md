# Task 11: Fix Incomplete ImageEdit Object in Feed

## Priority
Medium

## Category
ImageEdit Object Incomplete (Bug #3)

## Description
Ensure feed responses include complete imageEdit object data matching image completion module format. Currently, the imageEdit object in feed responses is missing data and should return the complete object response mapping as in the image completion module.

## Technical Details

### Files to Modify
- `src/feed/service/feed.service.ts` - buildImageEdit() method
- `src/feed/service/feed.service.ts` - Feed entity mapping logic
- Feed response building methods that handle imageEdit data

### Key Components
- **Method**: `FeedService.buildImageEdit()` - Currently returns simplified object
- **Target**: Use complete ImageEditResponseMapper for consistency
- **Relations**: Ensure imageEditImageCompletion.imageEdit relations are loaded
- **DTO**: Should match ImageEditDto structure from image completion module

### Current Implementation Gap
```typescript
// Current simplified implementation in FeedService
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }
  
  // Currently returns simplified object
  return {
    id: imageEdit.id,
    type: imageEdit.type,
    createdAt: imageEdit.createdAt,
    // Missing: complete ImageEditDto fields
  };
}
```

## Implementation Steps

1. **Analyze Current ImageEdit Mapping**
   - Review buildImageEdit() method in FeedService
   - Compare with ImageEditResponseMapper.map() method
   - Identify missing fields and data

2. **Integrate ImageEditResponseMapper**
   - Import ImageEditResponseMapper into FeedService
   - Replace simplified mapping with complete mapper usage
   - Ensure dependency injection is properly configured

3. **Update Feed Entity Queries**
   - Verify imageEditImageCompletion.imageEdit relations are loaded
   - Ensure all necessary imageEdit data is available
   - Update query builders to include complete imageEdit relations

4. **Test ImageEdit Completeness**
   - Verify feed responses include all ImageEditDto fields
   - Compare with direct image completion endpoint responses
   - Ensure consistency across API endpoints

## Testing Requirements

### Unit Tests
```typescript
describe('FeedService ImageEdit Mapping', () => {
  it('should return complete imageEdit object using ImageEditResponseMapper')
  it('should include all ImageEditDto fields in feed responses')
  it('should handle null imageEdit relations gracefully')
  it('should match image completion module format')
})
```

### Integration Tests
- Test feed endpoints return complete imageEdit objects
- Compare imageEdit structure with image completion endpoints
- Verify all expected fields are present

## Dependencies
- Task 12: Update FeedService imageEdit mapping (implementation details)
- Task 13: Use ImageEditResponseMapper in feed (mapper integration)

## Acceptance Criteria

1. **Complete ImageEdit Objects**
   - Feed responses include all ImageEditDto fields
   - ImageEdit structure matches image completion module format
   - No missing or simplified data in imageEdit objects

2. **Consistency Across Endpoints**
   - ImageEdit objects identical between feed and image completion endpoints
   - Same field names, types, and structure
   - Consistent data formatting and validation

3. **Performance Maintained**
   - Complete imageEdit mapping doesn't significantly impact performance
   - Efficient loading of imageEdit relations
   - No N+1 query issues with imageEdit data

4. **Backward Compatibility**
   - Existing API consumers continue to work
   - No breaking changes to response structure
   - ImageEdit field is properly typed and documented

## Expected ImageEdit Structure

### Current Simplified Structure
```json
{
  "imageEdit": {
    "id": "edit-uuid",
    "type": "inpaint",
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

### Target Complete Structure
```json
{
  "imageEdit": {
    "id": "edit-uuid",
    "mode": "inpaint",
    "imageCompletionsCount": 4,
    "width": 1024,
    "height": 1024,
    "mask": "base64-mask-data",
    "status": "completed",
    "settings": {
      "strength": 0.8,
      "guidance": 7.5
    },
    "inputImageUrls": ["https://cdn.example.com/input.jpg"],
    "webhookUrl": null,
    "hidePrompt": false,
    "createdAt": "2025-01-01T00:00:00Z",
    "models": [
      {
        "id": "model-uuid",
        "name": "Model Name",
        "version": "1.0"
      }
    ]
  }
}
```

## Implementation Approach

### Dependency Injection Update
```typescript
@Injectable()
export class FeedService {
  constructor(
    // ... existing dependencies ...
    private readonly imageEditResponseMapper: ImageEditResponseMapper, // Add this
  ) {}
}
```

### buildImageEdit Method Update
```typescript
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }

  try {
    const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;
    // Use complete mapper instead of simplified object
    return await this.imageEditResponseMapper.map(
      imageEdit,
      false, // isInternal
      true,  // allowNesting
      true   // includeImageEdits
    );
  } catch (error) {
    this.logger.debug('Error mapping image edit', {
      imageCompletionId: fullImageEntity.id,
      error: error.message,
    });
    return null;
  }
}
```
