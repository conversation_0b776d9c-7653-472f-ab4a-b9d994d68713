# Task 08: Ensure Video Queries Include User Relation

## Priority
High

## Category
Video User Object Missing (Bug #2)

## Description
Update VideoProvider methods to include user relation when fetching videos, particularly in getBy() and findByIdWithRelations() methods. This ensures that user data is available for the response mapper to populate the user field.

## Technical Details

### Files to Modify
- `src/video/service/provider.ts` - VideoProvider class
- `src/video/service/provider.ts:getBy()` method
- `src/video/service/provider.ts:findByIdWithRelations()` method
- `src/video/service/provider.ts:get()` method

### Key Methods to Update
- **getBy()**: Used by controller for single video retrieval
- **findByIdWithRelations()**: Explicit relation loading method
- **get()**: Basic video retrieval method
- **prepareQueryBuilder()**: Query building helper

### Current Implementation Analysis
```typescript
// Current getBy method likely missing user relation
async getBy(criteria: any): Promise<VideoEntity> {
  // Need to add user relation to this query
}

// findByIdWithRelations exists but may not include user by default
async findByIdWithRelations(id: string, relations: string[] = []): Promise<VideoEntity | null>
```

## Implementation Steps

1. **Update getBy() Method**
   - Add user relation to the default relations array
   - Ensure user data is loaded for standard video queries
   - Maintain backward compatibility

2. **Update findByIdWithRelations() Method**
   - Include user in default relations if not specified
   - Allow explicit user relation control
   - Update relation building logic

3. **Update get() Method**
   - Add user relation loading option
   - Ensure consistency with getBy() behavior

4. **Update Query Builder Methods**
   - Modify prepareQueryBuilder() to optionally include user relation
   - Ensure efficient JOIN operations
   - Avoid N+1 query issues

## Testing Requirements

### Unit Tests
```typescript
describe('VideoProvider User Relations', () => {
  it('should include user relation in getBy() queries')
  it('should include user relation in findByIdWithRelations()')
  it('should load user data efficiently (no N+1 queries)')
  it('should handle missing user relations gracefully')
})
```

### Integration Tests
- Test that video queries actually load user data from database
- Verify JOIN operations are efficient
- Test with various video scenarios (with/without users)

## Dependencies
- Task 09: Update video entity relations (ensure proper entity configuration)

## Acceptance Criteria

1. **User Relations Loaded**
   - `getBy()` method includes user relation by default
   - `findByIdWithRelations()` includes user when requested
   - User data is available for response mapping

2. **Query Efficiency**
   - User relation loaded with single JOIN, not separate query
   - No N+1 query issues when loading multiple videos
   - Database query performance maintained or improved

3. **Backward Compatibility**
   - Existing code continues to work without changes
   - Optional user relation loading where appropriate
   - No breaking changes to method signatures

4. **Error Handling**
   - Graceful handling of missing user relations
   - Proper error messages for invalid queries
   - Fallback behavior when user data unavailable

## Implementation Code

### getBy() Method Update
```typescript
async getBy(criteria: any): Promise<VideoEntity> {
  return this.repository.findOne({
    where: criteria,
    relations: {
      user: true, // Add user relation
      originalImageCompletion: {
        user: true, // Also include user for original image
      },
    },
  });
}
```

### findByIdWithRelations() Method Update
```typescript
async findByIdWithRelations(
  id: string,
  relations: string[] = [],
): Promise<VideoEntity | null> {
  try {
    const relationOptions: any = {};

    // Always include user relation unless explicitly excluded
    if (!relations.includes('-user')) {
      relationOptions.user = true;
    }

    // Build other relations based on requested relations
    relations.forEach((relation) => {
      if (relation === 'user') {
        relationOptions.user = true;
      } else if (relation === 'organization') {
        relationOptions.organization = true;
      } else if (relation === 'originalImageCompletion') {
        relationOptions.originalImageCompletion = {
          user: true,
        };
      }
    });

    return await this.repository.findOne({
      where: { id },
      relations: relationOptions,
    });
  } catch (error) {
    this.logger.error('Failed to find video by ID with relations', {
      id,
      relations,
      error: error.message,
    });
    throw error;
  }
}
```

### get() Method Update
```typescript
async get(id: string): Promise<VideoEntity> {
  const video = await this.repository.findOne({
    where: { id },
    relations: {
      user: true, // Include user relation
    },
  });

  if (!video) {
    throw new NotFoundException(`Video with ID ${id} not found`);
  }

  return video;
}
```

### Query Builder Helper
```typescript
private buildRelationsObject(includeUser = true): any {
  const relations: any = {};

  if (includeUser) {
    relations.user = true;
  }

  // Add other default relations as needed
  relations.originalImageCompletion = {
    user: true,
  };

  return relations;
}
```
