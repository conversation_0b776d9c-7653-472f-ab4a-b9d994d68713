# Task 07: Update VideoResponseMapper to Include User

## Priority
High

## Category
Video User Object Missing (Bug #2)

## Description
Modify VideoResponseMapper.map() method to populate the user field from the video entity's user relation. This is the core implementation task to ensure user data is properly mapped from the database entity to the response DTO.

## Technical Details

### Files to Modify
- `src/video/service/response-mapper.ts` - Main implementation file
- `src/video/service/response-mapper.ts:map()` method - Core mapping logic
- `src/video/service/response-mapper.ts:mapInternal()` method - Internal mapping

### Key Components
- **Class**: `VideoResponseMapper`
- **Method**: `map(entity: VideoEntity, userId: string = null): Promise<VideoDto>`
- **Dependencies**: `UserResponseMapper` (for consistent user formatting)
- **Entity**: `VideoEntity.user` relation
- **DTO**: `VideoDto.user` field

### Current Implementation Analysis
```typescript
// Current map method structure (from codebase analysis)
async map(entity: VideoEntity, userId: string = null): Promise<VideoDto> {
  const dto = new VideoDto();
  dto.id = entity.id;
  // ... other field mappings ...
  // Missing: dto.user = ... (user field not populated)
  return dto;
}
```

## Implementation Steps

1. **Inject UserResponseMapper Dependency**
   - Add UserResponseMapper to VideoResponseMapper constructor
   - Ensure proper dependency injection configuration

2. **Update map() Method**
   - Add user field population logic
   - Use UserResponseMapper.mapPublic() for consistent formatting
   - Handle null/undefined user relation gracefully

3. **Update mapInternal() Method**
   - Ensure internal mapping also includes user data
   - Maintain consistency between public and internal mappings

4. **Update mapMultiple() Methods**
   - Ensure batch mapping operations include user data
   - Optimize for performance with multiple video mappings

## Testing Requirements

### Unit Tests
```typescript
describe('VideoResponseMapper.map()', () => {
  it('should populate user field when user relation exists')
  it('should handle null user relation gracefully')
  it('should use UserResponseMapper for consistent formatting')
  it('should include user data in mapInternal()')
  it('should include user data in mapMultiple()')
})
```

### Integration Tests
- Test with real VideoEntity objects that have user relations
- Verify user data structure matches PublicUserDto
- Test performance with multiple video mappings

## Dependencies
- Task 08: Ensure video queries include user relation (provider must load user data)

## Acceptance Criteria

1. **User Field Populated**
   - `dto.user` field is populated when entity.user exists
   - User data structure matches PublicUserDto format
   - Consistent formatting with other API endpoints

2. **Error Handling**
   - Gracefully handles null/undefined user relations
   - No errors when user data is missing
   - Appropriate fallback behavior defined

3. **Performance Optimized**
   - No N+1 query issues in batch operations
   - Efficient user data mapping
   - Minimal overhead for user field population

4. **Consistency Maintained**
   - Uses existing UserResponseMapper for formatting
   - Same user data structure across all API endpoints
   - Internal and public mappings both include user data

## Implementation Code

### Constructor Update
```typescript
@Injectable()
export class VideoResponseMapper {
  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => ImageCompletionResponseMapper))
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly userResponseMapper: UserResponseMapper, // Add this dependency
  ) {
    this.cdnHost = this.configService.get<string>('CDN_HOST');
  }
}
```

### Map Method Update
```typescript
async map(entity: VideoEntity, userId: string = null): Promise<VideoDto> {
  const dto = new VideoDto();
  
  dto.id = entity.id;
  
  // Add user mapping
  if (entity.user) {
    dto.user = this.userResponseMapper.mapPublic(entity.user);
  }
  
  // Map original image completion (existing code)
  if (entity.originalImageCompletion) {
    dto.originalImageCompletion = await this.imageCompletionResponseMapper.map(
      entity.originalImageCompletion,
      true,
      true,
      userId,
    );
    dto.originalImageCompletionId = entity.originalImageCompletionId;
  }
  
  // ... rest of existing mapping logic ...
  
  return dto;
}
```

### MapInternal Method Update
```typescript
async mapInternal(entity: VideoEntity): Promise<VideoDto> {
  const dto = await this.map(entity); // This will now include user data
  
  // Add internal-only fields
  dto.generationSeconds = entity.generationSeconds;
  
  return dto;
}
```

### Error Handling Considerations
```typescript
// Handle potential user relation issues
if (entity.user) {
  try {
    dto.user = this.userResponseMapper.mapPublic(entity.user);
  } catch (error) {
    // Log error but don't fail the entire mapping
    console.warn('Failed to map user data for video', entity.id, error);
    dto.user = null; // or appropriate fallback
  }
}
```
