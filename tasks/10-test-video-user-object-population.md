# Task 10: Test Video User Object Population

## Priority
High

## Category
Video User Object Missing (Bug #2)

## Description
Create tests to verify that GET /video/{id} returns complete user object including username, profile picture, and other public user data. This validates the entire user object population implementation from entity to response.

## Technical Details

### Files to Create/Modify
- `src/video/service/response-mapper.spec.ts` - Unit tests for mapper
- `src/video/service/provider.spec.ts` - Unit tests for provider
- `test/integration/video/video-user-object.spec.ts` - Integration tests
- `test/fixtures/video-test-data.ts` - Test data setup

### Test Coverage Areas
- **Unit Tests**: VideoResponseMapper user field population
- **Provider Tests**: User relation loading in queries
- **Integration Tests**: Complete API endpoint response validation
- **Edge Cases**: Missing user data, null relations, error scenarios

### Expected User Object Structure
```typescript
interface ExpectedUserObject {
  id: string;
  username: string;
  name: string;
  profilePicture: string;
  description?: string;
  website?: string;
  isVerified: boolean;
  followersCount: number;
  followingCount: number;
}
```

## Implementation Steps

1. **Create Unit Tests for VideoResponseMapper**
   - Test user field population when entity.user exists
   - Test graceful handling when entity.user is null
   - Test user data structure matches PublicUserDto

2. **Create Unit Tests for VideoProvider**
   - Test that getBy() includes user relation
   - Test that findByIdWithRelations() loads user data
   - Test query efficiency (no N+1 queries)

3. **Create Integration Tests**
   - Test GET /video/{id} endpoint returns user object
   - Test user object completeness and structure
   - Test with different user types and permissions

4. **Create Edge Case Tests**
   - Test videos with missing user relations
   - Test deleted user scenarios
   - Test performance with multiple video requests

## Testing Requirements

### Unit Test Cases
```typescript
describe('VideoResponseMapper User Population', () => {
  it('should populate user field when entity.user exists')
  it('should handle null entity.user gracefully')
  it('should use UserResponseMapper for consistent formatting')
  it('should include all required user fields')
  it('should not include sensitive user data')
})

describe('VideoProvider User Relations', () => {
  it('should include user relation in getBy() queries')
  it('should load user data with single query (no N+1)')
  it('should handle missing user relations without errors')
})
```

### Integration Test Cases
```typescript
describe('GET /video/:id User Object', () => {
  it('should return complete user object in response')
  it('should include username, name, and profile picture')
  it('should include public user statistics')
  it('should not include sensitive user information')
  it('should work for both public and authenticated requests')
})
```

### Test Data Requirements
- Test users with complete profile information
- Test videos associated with different users
- Test scenarios with missing user data

## Dependencies
- Task 06: Fix Missing User Object in Video Responses (main implementation)
- Task 07: Update VideoResponseMapper to include user
- Task 08: Ensure video queries include user relation
- Task 09: Update video entity relations

## Acceptance Criteria

1. **Complete User Object Returned**
   - GET /video/{id} responses include user object
   - User object contains all expected public fields
   - User data structure matches other API endpoints

2. **Data Accuracy Validated**
   - User information matches actual user data
   - Profile pictures, usernames, and stats are correct
   - No sensitive data (emails, passwords) included

3. **Performance Verified**
   - User data loaded efficiently (single query)
   - No performance degradation from user object inclusion
   - Batch operations maintain efficiency

4. **Error Handling Tested**
   - Graceful handling of missing user data
   - Appropriate responses for deleted users
   - No crashes when user relations are null

## Test Implementation Examples

### Unit Test Structure
```typescript
describe('VideoResponseMapper', () => {
  let mapper: VideoResponseMapper;
  let mockUserResponseMapper: jest.Mocked<UserResponseMapper>;

  beforeEach(() => {
    mockUserResponseMapper = createMockUserResponseMapper();
    mapper = new VideoResponseMapper(
      mockConfigService,
      mockImageCompletionResponseMapper,
      mockUserResponseMapper,
    );
  });

  it('should populate user field when entity.user exists', async () => {
    const mockUser = createMockUserEntity();
    const mockVideo = createMockVideoEntity({ user: mockUser });
    const expectedUserDto = createMockPublicUserDto();

    mockUserResponseMapper.mapPublic.mockReturnValue(expectedUserDto);

    const result = await mapper.map(mockVideo);

    expect(result.user).toEqual(expectedUserDto);
    expect(mockUserResponseMapper.mapPublic).toHaveBeenCalledWith(mockUser);
  });
});
```

### Integration Test Structure
```typescript
describe('GET /video/:id User Object Integration', () => {
  let app: INestApplication;
  let testUser: UserEntity;
  let testVideo: VideoEntity;

  beforeAll(async () => {
    app = await createTestApp();
    testUser = await createTestUser({
      username: 'testcreator',
      name: 'Test Creator',
      profilePicture: 'https://example.com/profile.jpg',
    });
    testVideo = await createTestVideo({ user: testUser });
  });

  it('should return complete user object in video response', async () => {
    const response = await request(app)
      .get(`/video/${testVideo.id}`)
      .expect(200);

    expect(response.body.user).toBeDefined();
    expect(response.body.user).toMatchObject({
      id: testUser.id,
      username: 'testcreator',
      name: 'Test Creator',
      profilePicture: 'https://example.com/profile.jpg',
      isVerified: false,
    });

    // Ensure sensitive data is not included
    expect(response.body.user.email).toBeUndefined();
    expect(response.body.user.password).toBeUndefined();
  });
});
```

### Performance Test
```typescript
it('should load user data efficiently for multiple videos', async () => {
  const videos = await createMultipleTestVideos(10);
  
  const startTime = Date.now();
  const responses = await Promise.all(
    videos.map(video => 
      request(app).get(`/video/${video.id}`).expect(200)
    )
  );
  const endTime = Date.now();

  // Verify all responses include user data
  responses.forEach(response => {
    expect(response.body.user).toBeDefined();
    expect(response.body.user.username).toBeDefined();
  });

  // Verify reasonable performance (adjust threshold as needed)
  expect(endTime - startTime).toBeLessThan(2000); // 2 seconds for 10 videos
});
```
