# Task 13: Use ImageEditResponseMapper in Feed

## Priority
Medium

## Category
ImageEdit Object Incomplete (Bug #3)

## Description
Integrate ImageEditResponseMapper.map() method in feed service to ensure consistent imageEdit object structure. This task focuses on the proper integration and configuration of the existing ImageEditResponseMapper within the feed service context.

## Technical Details

### Files to Modify
- `src/feed/feed.module.ts` - Module imports and providers
- `src/feed/service/feed.service.ts` - Dependency injection
- `src/image-edit/service/response-mapper.ts` - Verify mapper compatibility

### Key Integration Points
- **Module Import**: Ensure ImageEditModule is imported in FeedModule
- **Dependency Injection**: Proper injection of ImageEditResponseMapper
- **Mapper Usage**: Correct parameters for feed context
- **Error Handling**: Integration-specific error scenarios

### ImageEditResponseMapper Interface
```typescript
// From image-edit/service/response-mapper.ts
async map(
  entity: ImageEditEntity,
  isInternal = false,
  allowNesting = true,
  includeImageEdits = true,
): Promise<ImageEditDto>
```

## Implementation Steps

1. **Update FeedModule Configuration**
   - Import ImageEditModule if not already imported
   - Ensure ImageEditResponseMapper is available
   - Check for any circular dependency issues

2. **Configure Dependency Injection**
   - Add ImageEditResponseMapper to FeedService constructor
   - Use proper injection decorators if needed
   - Handle any forwardRef requirements

3. **Verify Mapper Compatibility**
   - Test ImageEditResponseMapper with feed entity data
   - Ensure mapper handles feed-specific entity structures
   - Validate parameter combinations for feed context

4. **Handle Integration Edge Cases**
   - Test with various imageEdit entity states
   - Handle missing relations gracefully
   - Ensure performance is acceptable in feed context

## Testing Requirements

### Unit Tests
```typescript
describe('ImageEditResponseMapper Integration', () => {
  it('should be properly injected into FeedService')
  it('should map imageEdit entities correctly in feed context')
  it('should handle feed-specific entity structures')
  it('should maintain performance in batch operations')
})
```

### Integration Tests
- Test feed endpoints with imageEdit data
- Verify mapper integration doesn't break existing functionality
- Test error scenarios and fallback behavior

## Dependencies
- Task 12: Update FeedService imageEdit mapping (method implementation)

## Acceptance Criteria

1. **Proper Module Integration**
   - ImageEditModule imported in FeedModule
   - ImageEditResponseMapper available for injection
   - No circular dependency issues

2. **Correct Dependency Injection**
   - ImageEditResponseMapper properly injected into FeedService
   - Constructor parameters correctly configured
   - No runtime injection errors

3. **Mapper Functionality**
   - ImageEditResponseMapper works correctly with feed entity data
   - Proper parameter configuration for feed context
   - Consistent output with image completion module

4. **Error Handling**
   - Graceful handling of mapper integration failures
   - Appropriate fallback behavior
   - Clear error logging for debugging

## Implementation Code

### FeedModule Update
```typescript
// src/feed/feed.module.ts
import { Module } from '@nestjs/common';
import { ImageEditModule } from 'src/image-edit/image-edit.module';
import { FeedService } from './service/feed.service';

@Module({
  imports: [
    // ... existing imports ...
    ImageEditModule, // Add ImageEditModule import
  ],
  providers: [
    FeedService,
    // ... other providers ...
  ],
  exports: [FeedService],
})
export class FeedModule {}
```

### FeedService Constructor Update
```typescript
// src/feed/service/feed.service.ts
import { ImageEditResponseMapper } from 'src/image-edit/service/response-mapper';

@Injectable()
export class FeedService {
  constructor(
    // ... existing dependencies ...
    private readonly imageEditResponseMapper: ImageEditResponseMapper,
    private readonly logger: Logger,
  ) {}
}
```

### Mapper Usage in buildImageEdit
```typescript
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }

  try {
    const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;
    
    // Use ImageEditResponseMapper with feed-appropriate parameters
    return await this.imageEditResponseMapper.map(
      imageEdit,
      false, // isInternal: false for public feed responses
      true,  // allowNesting: true to include nested objects
      true   // includeImageEdits: true for complete edit data
    );
  } catch (error) {
    this.logger.debug('Error mapping image edit in feed', {
      imageCompletionId: fullImageEntity.id,
      error: error.message,
    });
    return null;
  }
}
```

### Circular Dependency Handling (if needed)
```typescript
// If circular dependency issues arise
import { forwardRef, Inject } from '@nestjs/common';

@Injectable()
export class FeedService {
  constructor(
    // ... other dependencies ...
    @Inject(forwardRef(() => ImageEditResponseMapper))
    private readonly imageEditResponseMapper: ImageEditResponseMapper,
  ) {}
}
```

### Module Provider Configuration (alternative approach)
```typescript
// If direct module import doesn't work
@Module({
  imports: [
    // ... existing imports ...
  ],
  providers: [
    FeedService,
    {
      provide: ImageEditResponseMapper,
      useFactory: (configService, modelResponseMapper) => {
        return new ImageEditResponseMapper(configService, modelResponseMapper);
      },
      inject: [ConfigService, ModelResponseMapper],
    },
    // ... other providers ...
  ],
})
export class FeedModule {}
```

### Integration Test Example
```typescript
describe('ImageEditResponseMapper Feed Integration', () => {
  let feedService: FeedService;
  let imageEditResponseMapper: ImageEditResponseMapper;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [FeedModule, ImageEditModule],
    }).compile();

    feedService = module.get<FeedService>(FeedService);
    imageEditResponseMapper = module.get<ImageEditResponseMapper>(ImageEditResponseMapper);
  });

  it('should have ImageEditResponseMapper properly injected', () => {
    expect(feedService).toBeDefined();
    expect(imageEditResponseMapper).toBeDefined();
  });

  it('should map imageEdit objects correctly in feed context', async () => {
    const mockImageEntity = createMockImageEntityWithEdit();
    const result = await feedService.buildImageEdit(mockImageEntity);
    
    expect(result).toBeDefined();
    expect(result.id).toBeDefined();
    expect(result.mode).toBeDefined();
    expect(result.settings).toBeDefined();
  });
});
```
