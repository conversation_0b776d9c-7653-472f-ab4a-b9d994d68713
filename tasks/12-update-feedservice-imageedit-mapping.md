# Task 12: Update FeedService ImageEdit Mapping

## Priority
Medium

## Category
ImageEdit Object Incomplete (Bug #3)

## Description
Modify buildImageEdit() method in FeedService to return complete ImageEditDto instead of simplified object. This is the core implementation task to ensure imageEdit objects in feed responses match the complete structure from the image completion module.

## Technical Details

### Files to Modify
- `src/feed/service/feed.service.ts` - buildImageEdit() method
- `src/feed/service/feed.service.ts` - Constructor for dependency injection
- Feed service methods that call buildImageEdit()

### Key Method to Update
- **Method**: `buildImageEdit(fullImageEntity: any): Promise<any>`
- **Location**: Line ~1544 in feed.service.ts
- **Current**: Returns simplified object with id, type, createdAt
- **Target**: Return complete ImageEditDto using ImageEditResponseMapper

### Current Implementation
```typescript
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }

  try {
    const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;
    // Current simplified mapping
    return {
      id: imageEdit.id,
      type: imageEdit.type,
      createdAt: imageEdit.createdAt,
      // Missing: complete ImageEditDto fields
    };
  } catch (error) {
    this.logger.debug('Error mapping image edit', {
      imageCompletionId: fullImageEntity.id,
      error: error.message,
    });
    return null;
  }
}
```

## Implementation Steps

1. **Add ImageEditResponseMapper Dependency**
   - Import ImageEditResponseMapper into FeedService
   - Add to constructor dependency injection
   - Ensure proper module imports

2. **Update buildImageEdit() Method**
   - Replace simplified object creation with mapper usage
   - Use ImageEditResponseMapper.map() method
   - Pass appropriate parameters for feed context

3. **Handle Mapping Parameters**
   - Determine correct parameters for mapper (isInternal, allowNesting, etc.)
   - Ensure feed context doesn't break imageEdit mapping
   - Handle any feed-specific requirements

4. **Update Error Handling**
   - Maintain existing error handling patterns
   - Add specific error handling for mapper failures
   - Ensure graceful degradation when mapping fails

## Testing Requirements

### Unit Tests
```typescript
describe('FeedService.buildImageEdit()', () => {
  it('should use ImageEditResponseMapper for complete mapping')
  it('should return complete ImageEditDto structure')
  it('should handle null imageEdit relations gracefully')
  it('should maintain error handling for mapping failures')
  it('should pass correct parameters to mapper')
})
```

### Integration Tests
- Test that feed responses include complete imageEdit objects
- Verify imageEdit structure matches ImageEditDto
- Test error scenarios and graceful degradation

## Dependencies
- Task 13: Use ImageEditResponseMapper in feed (mapper integration)

## Acceptance Criteria

1. **Complete ImageEdit Mapping**
   - buildImageEdit() uses ImageEditResponseMapper.map()
   - Returns complete ImageEditDto structure
   - All imageEdit fields properly populated

2. **Proper Dependency Injection**
   - ImageEditResponseMapper properly injected into FeedService
   - No circular dependency issues
   - Module imports correctly configured

3. **Error Handling Maintained**
   - Existing error handling patterns preserved
   - Graceful handling of mapper failures
   - Appropriate logging for debugging

4. **Performance Considerations**
   - Complete mapping doesn't significantly impact performance
   - Efficient use of mapper methods
   - No unnecessary data loading

## Implementation Code

### Constructor Update
```typescript
@Injectable()
export class FeedService {
  constructor(
    private readonly feedEntryProvider: FeedEntryProvider,
    private readonly globalFeedCacheProvider: GlobalFeedCacheProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly videoProvider: VideoProvider,
    private readonly userProvider: UserProvider,
    private readonly likeProvider: EntityLikeProvider,
    private readonly bookmarkProvider: BookmarkProvider,
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly videoResponseMapper: VideoResponseMapper,
    private readonly userResponseMapper: UserResponseMapper,
    private readonly imageEditResponseMapper: ImageEditResponseMapper, // Add this
    private readonly logger: Logger,
  ) {}
}
```

### buildImageEdit Method Update
```typescript
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }

  try {
    const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;
    
    // Use complete mapper instead of simplified object
    return await this.imageEditResponseMapper.map(
      imageEdit,
      false, // isInternal - false for public feed responses
      true,  // allowNesting - true to include nested data
      true   // includeImageEdits - true to include complete edit data
    );
  } catch (error) {
    this.logger.debug('Error mapping image edit', {
      imageCompletionId: fullImageEntity.id,
      imageEditId: fullImageEntity.imageEditImageCompletion?.imageEdit?.id,
      error: error.message,
    });
    return null;
  }
}
```

### Module Import Update (if needed)
```typescript
// In feed.module.ts
import { ImageEditResponseMapper } from 'src/image-edit/service/response-mapper';

@Module({
  imports: [
    // ... existing imports ...
    ImageEditModule, // Ensure ImageEditModule is imported
  ],
  providers: [
    FeedService,
    ImageEditResponseMapper, // Add if not already provided by ImageEditModule
    // ... other providers ...
  ],
})
export class FeedModule {}
```

### Error Handling Enhancement
```typescript
private async buildImageEdit(fullImageEntity: any): Promise<any> {
  if (!fullImageEntity?.imageEditImageCompletion?.imageEdit) {
    return null;
  }

  try {
    const imageEdit = fullImageEntity.imageEditImageCompletion.imageEdit;
    
    // Validate imageEdit entity before mapping
    if (!imageEdit.id) {
      this.logger.warn('ImageEdit entity missing required fields', {
        imageCompletionId: fullImageEntity.id,
        imageEdit,
      });
      return null;
    }
    
    return await this.imageEditResponseMapper.map(
      imageEdit,
      false, // isInternal
      true,  // allowNesting
      true   // includeImageEdits
    );
  } catch (error) {
    this.logger.error('Failed to map image edit in feed', {
      imageCompletionId: fullImageEntity.id,
      imageEditId: fullImageEntity.imageEditImageCompletion?.imageEdit?.id,
      error: error.message,
      stack: error.stack,
    });
    
    // Return null to gracefully handle mapping failures
    return null;
  }
}
```
