# Task 09: Update Video Entity Relations

## Priority
High

## Category
Video User Object Missing (Bug #2)

## Description
Verify VideoEntity has proper user relation mapping and update if necessary. This ensures the database entity correctly defines the relationship between videos and users, enabling proper data loading in queries.

## Technical Details

### Files to Analyze/Modify
- `src/video/entity/video.entity.ts` - VideoEntity class definition
- Database migration files (if relation changes needed)
- Related entity files for consistency check

### Key Relation Requirements
- **@ManyToOne**: Video belongs to one User
- **@JoinColumn**: Proper foreign key configuration
- **Lazy Loading**: Relation should be lazy-loaded by default
- **Cascade Options**: Appropriate cascade behavior

### Expected Relation Structure
```typescript
@ManyToOne(() => UserEntity, { eager: false })
@JoinColumn({ name: 'userId' })
user: UserEntity;

@Column({ type: 'uuid' })
userId: string;
```

## Implementation Steps

1. **Analyze Current Entity Definition**
   - Review existing VideoEntity class
   - Check if user relation already exists
   - Verify relation configuration is correct

2. **Update Entity Relations if Needed**
   - Add @ManyToOne relation to UserEntity
   - Configure proper @JoinColumn with foreign key
   - Set appropriate relation options (eager: false)

3. **Verify Database Schema**
   - Ensure userId column exists in videos table
   - Check foreign key constraints are properly set
   - Create migration if schema changes needed

4. **Update Related Entities**
   - Check UserEntity for corresponding @OneToMany relation
   - Ensure bidirectional relation consistency
   - Update any other entities that reference videos

## Testing Requirements

### Entity Tests
```typescript
describe('VideoEntity Relations', () => {
  it('should have proper user relation mapping')
  it('should load user relation when requested')
  it('should maintain foreign key constraints')
  it('should handle null user relations gracefully')
})
```

### Database Tests
- Test relation loading from database
- Verify foreign key constraints work
- Test cascade behavior if applicable

## Dependencies
None - this is a foundational entity configuration

## Acceptance Criteria

1. **Proper Relation Configuration**
   - VideoEntity has @ManyToOne relation to UserEntity
   - @JoinColumn properly configured with userId foreign key
   - Relation is lazy-loaded by default (eager: false)

2. **Database Schema Consistency**
   - videos table has userId column with proper type (UUID)
   - Foreign key constraint exists between videos.userId and users.id
   - Database schema matches entity definition

3. **Bidirectional Relations**
   - UserEntity has corresponding @OneToMany relation to videos (if needed)
   - Relations are properly configured for both directions
   - No circular dependency issues

4. **Performance Considerations**
   - Relation is lazy-loaded to avoid unnecessary data loading
   - Proper indexing on userId column for query performance
   - No impact on existing video queries

## Implementation Code

### VideoEntity Relation Definition
```typescript
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { UserEntity } from 'src/user/entity/user.entity';

@Entity('videos')
export class VideoEntity {
  @Column({ type: 'uuid' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // User relation
  @ManyToOne(() => UserEntity, { eager: false, nullable: false })
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column({ type: 'uuid', nullable: false })
  userId: string;

  // ... other existing fields ...
}
```

### UserEntity Corresponding Relation (if needed)
```typescript
import { Entity, OneToMany } from 'typeorm';
import { VideoEntity } from 'src/video/entity/video.entity';

@Entity('users')
export class UserEntity {
  // ... existing fields ...

  @OneToMany(() => VideoEntity, video => video.user)
  videos: VideoEntity[];
}
```

### Database Migration (if needed)
```typescript
// migration file: add-user-relation-to-videos.ts
export class AddUserRelationToVideos implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add userId column if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE videos 
      ADD COLUMN IF NOT EXISTS "userId" uuid NOT NULL
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE videos 
      ADD CONSTRAINT "FK_videos_userId" 
      FOREIGN KEY ("userId") REFERENCES users("id") 
      ON DELETE CASCADE
    `);

    // Add index for performance
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_videos_userId" 
      ON videos ("userId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_videos_userId"`);
    await queryRunner.query(`ALTER TABLE videos DROP CONSTRAINT IF EXISTS "FK_videos_userId"`);
    await queryRunner.query(`ALTER TABLE videos DROP COLUMN IF EXISTS "userId"`);
  }
}
```

### Verification Queries
```sql
-- Check if relation exists in database
SELECT 
  tc.table_name, 
  kcu.column_name, 
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'videos'
  AND kcu.column_name = 'userId';
```
