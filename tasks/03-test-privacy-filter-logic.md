# Task 03: Test Privacy Filter Logic

## Priority
High

## Category
User Feed Privacy/ModelIds Filters (Bug #1)

## Description
Verify that privacy=public only shows public content to non-owners, privacy=private shows private content to owners, and privacy=all works correctly for owners. Create comprehensive tests to validate privacy filtering behavior.

## Technical Details

### Files to Create/Modify
- `src/feed/service/feed.service.spec.ts` - Unit tests
- `test/integration/feed/user-feed-privacy.spec.ts` - Integration tests
- Test data setup for privacy scenarios

### Key Test Scenarios
- **Public Filter**: Non-owners can only see public content
- **Private Filter**: Only owners can see private content
- **All Filter**: Only owners can see all content (public + private)
- **Permission Validation**: Non-owners cannot access private content

### Privacy Filter Values
```typescript
enum UserFeedPrivacyFilter {
  PUBLIC = 'public',
  PRIVATE = 'private', 
  ALL = 'all'
}
```

## Implementation Steps

1. **Create Test Data Setup**
   - Create test users (owner, non-owner)
   - Create test content with different privacy levels
   - Set up database test fixtures

2. **Unit Tests for Privacy Logic**
   - Test privacy filter application in service methods
   - Mock database queries and verify WHERE clauses
   - Test edge cases and error conditions

3. **Integration Tests for API Endpoint**
   - Test GET /feed/user/{userIdentifier} with different privacy filters
   - Test with authenticated owner vs non-owner requests
   - Verify response content matches privacy expectations

4. **Permission Validation Tests**
   - Ensure non-owners cannot see private content regardless of filter
   - Test unauthorized access attempts
   - Verify proper error responses

## Testing Requirements

### Unit Test Cases
```typescript
describe('Privacy Filter Logic', () => {
  it('should filter public content for non-owners')
  it('should show private content to owners only')
  it('should show all content to owners with all filter')
  it('should deny private access to non-owners')
  it('should handle null/undefined privacy filter')
})
```

### Integration Test Cases
```typescript
describe('GET /feed/user/:id Privacy', () => {
  it('should return only public content with privacy=public')
  it('should return private content to owner with privacy=private')
  it('should return all content to owner with privacy=all')
  it('should return 403 for non-owner accessing private')
  it('should default to public filter when not specified')
})
```

### Test Data Requirements
- User A (content owner) with public and private content
- User B (non-owner) for permission testing
- Mixed content types (images, videos) with different privacy levels

## Dependencies
- Task 02: Audit UserFeedService implementation (to understand current state)

## Acceptance Criteria

1. **Comprehensive Test Coverage**
   - All privacy filter combinations tested
   - Owner vs non-owner scenarios covered
   - Edge cases and error conditions included

2. **Privacy Rules Enforced**
   - `privacy=public`: Only public content visible to all users
   - `privacy=private`: Private content only visible to owners
   - `privacy=all`: All content only visible to owners
   - Non-owners never see private content regardless of filter

3. **API Security Validated**
   - Unauthorized access properly blocked
   - Appropriate HTTP status codes returned
   - No data leakage in error responses

4. **Test Documentation**
   - Clear test descriptions and expectations
   - Test data setup documented
   - Privacy rules clearly explained in comments

## Test Implementation Examples

### Unit Test Structure
```typescript
it('should apply public privacy filter correctly', async () => {
  const request = { privacy: UserFeedPrivacyFilter.PUBLIC };
  const mockQueryBuilder = createMockQueryBuilder();
  
  await feedService.getUserFeed('user-id', null, request);
  
  expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
    'entity.privacy = :privacy', 
    { privacy: 'public' }
  );
});
```

### Integration Test Structure
```typescript
it('should return only public content to non-owner', async () => {
  const response = await request(app)
    .get('/feed/user/owner-id?privacy=public')
    .set('Authorization', `Bearer ${nonOwnerToken}`)
    .expect(200);
    
  expect(response.body.items).toHaveLength(2); // Only public items
  expect(response.body.items.every(item => item.privacy === 'public')).toBe(true);
});
```
