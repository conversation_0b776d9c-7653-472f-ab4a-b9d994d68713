# Task 05: Add Integration Tests for User Feed Filters

## Priority
High

## Category
User Feed Privacy/ModelIds Filters (Bug #1)

## Description
Create comprehensive integration tests covering privacy and modelIds filtering scenarios for the user feed endpoint. These tests will validate the complete request-response cycle and ensure filters work correctly in a real API environment.

## Technical Details

### Files to Create
- `test/integration/feed/user-feed-filters.spec.ts` - Main integration test suite
- `test/fixtures/user-feed-test-data.ts` - Test data setup
- `test/helpers/feed-test-helpers.ts` - Reusable test utilities

### Test Environment Setup
- **Database**: Clean test database with controlled data
- **Authentication**: Test tokens for different user types
- **API Client**: Supertest for HTTP request testing
- **Data Fixtures**: Consistent test data across test runs

### Integration Test Scope
- Full HTTP request/response cycle
- Database query execution
- Authentication and authorization
- Filter combination scenarios
- Error handling and validation

## Implementation Steps

1. **Create Test Infrastructure**
   - Set up integration test environment
   - Create test database fixtures
   - Configure authentication helpers

2. **Implement Privacy Filter Tests**
   - Test all privacy filter values with different user types
   - Verify response data matches filter expectations
   - Test unauthorized access scenarios

3. **Implement ModelIds Filter Tests**
   - Test various modelIds array configurations
   - Verify content filtering by model associations
   - Test validation error scenarios

4. **Implement Combined Filter Tests**
   - Test privacy + modelIds combinations
   - Verify complex filtering scenarios
   - Test performance with multiple filters

## Testing Requirements

### Test Data Setup
```typescript
// Test users
const contentOwner = createTestUser('owner');
const regularUser = createTestUser('regular');

// Test models
const modelA = createTestModel('model-a-uuid');
const modelB = createTestModel('model-b-uuid');

// Test content with different privacy/model combinations
const publicContentModelA = createTestContent({ privacy: 'public', modelId: modelA.id });
const privateContentModelA = createTestContent({ privacy: 'private', modelId: modelA.id });
const publicContentModelB = createTestContent({ privacy: 'public', modelId: modelB.id });
```

### Privacy Integration Tests
```typescript
describe('User Feed Privacy Integration', () => {
  it('GET /feed/user/:id?privacy=public returns public content only')
  it('GET /feed/user/:id?privacy=private returns private content to owner')
  it('GET /feed/user/:id?privacy=all returns all content to owner')
  it('GET /feed/user/:id?privacy=private returns 403 to non-owner')
  it('GET /feed/user/:id defaults to public privacy filter')
})
```

### ModelIds Integration Tests
```typescript
describe('User Feed ModelIds Integration', () => {
  it('GET /feed/user/:id?modelIds=uuid1 returns content from model only')
  it('GET /feed/user/:id?modelIds=uuid1,uuid2 returns content from both models')
  it('GET /feed/user/:id?modelIds=nonexistent returns empty results')
  it('GET /feed/user/:id?modelIds=invalid returns 400 validation error')
  it('GET /feed/user/:id without modelIds returns content from all models')
})
```

### Combined Filter Tests
```typescript
describe('User Feed Combined Filters', () => {
  it('should apply privacy=public and modelIds together')
  it('should apply privacy=private and modelIds for owner')
  it('should handle complex filter combinations correctly')
  it('should maintain performance with multiple filters')
})
```

## Dependencies
- Task 01: Fix User Feed Privacy and ModelIds Filters (implementation)
- Task 02: Audit UserFeedService implementation
- Task 03: Test privacy filter logic
- Task 04: Test modelIds array filtering

## Acceptance Criteria

1. **Complete Integration Coverage**
   - All privacy filter scenarios tested end-to-end
   - All modelIds filter scenarios tested end-to-end
   - Combined filter scenarios validated
   - Error cases properly tested

2. **Real API Validation**
   - HTTP status codes correct for all scenarios
   - Response data structure matches expectations
   - Authentication/authorization working correctly
   - Validation errors properly formatted

3. **Performance Validation**
   - Filter queries execute efficiently
   - Response times acceptable for complex filters
   - Database query optimization verified

4. **Test Reliability**
   - Tests run consistently in CI/CD environment
   - Test data setup/teardown works correctly
   - No test interdependencies or race conditions

## Test Implementation Structure

### Main Test Suite
```typescript
describe('User Feed Filters Integration', () => {
  beforeAll(async () => {
    await setupTestDatabase();
    await createTestFixtures();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('Privacy Filters', () => {
    // Privacy-specific integration tests
  });

  describe('ModelIds Filters', () => {
    // ModelIds-specific integration tests
  });

  describe('Combined Filters', () => {
    // Combined filter integration tests
  });
});
```

### Example Integration Test
```typescript
it('should return only public content from specified models', async () => {
  const modelIds = [testModelA.id, testModelB.id];
  
  const response = await request(app)
    .get(`/feed/user/${contentOwner.id}`)
    .query({ privacy: 'public', modelIds })
    .set('Authorization', `Bearer ${regularUserToken}`)
    .expect(200);

  expect(response.body.items).toHaveLength(2);
  expect(response.body.items.every(item => 
    item.privacy === 'public' && modelIds.includes(item.modelId)
  )).toBe(true);
});
```
