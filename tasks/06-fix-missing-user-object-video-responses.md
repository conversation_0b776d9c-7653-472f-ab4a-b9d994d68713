# Task 06: Fix Missing User Object in Video Responses

## Priority
High

## Category
Video User Object Missing (Bug #2)

## Description
Add user object population to GET /video/{id} endpoint responses. Currently, the VideoDto has a user field defined, but it's not being populated in the response, making it impossible for frontend applications to display video creator information without additional API calls.

## Technical Details

### Files to Modify
- `src/video/service/response-mapper.ts` - VideoResponseMapper.map() method
- `src/video/service/provider.ts` - VideoProvider query methods
- `src/video/entity/video.entity.ts` - Verify user relation mapping
- `src/video/controller/read.controller.ts` - Verify controller usage

### Key Components
- **Controller**: `VideoReadController.get()` method
- **Service**: `VideoProvider.getBy()` and `VideoProvider.get()` methods
- **Mapper**: `VideoResponseMapper.map()` method
- **DTO**: `VideoDto.user` field (already defined)
- **Entity**: `VideoEntity.user` relation

### Current Implementation Gap
```typescript
// VideoDto defines user field but it's not populated
export class VideoDto {
  @ApiProperty({ description: 'User who created the video' })
  user: PublicUserDto; // This field is not being populated
}
```

## Implementation Steps

1. **Update VideoEntity Relations**
   - Verify VideoEntity has proper @ManyToOne relation to UserEntity
   - Ensure relation is properly configured for eager/lazy loading

2. **Update VideoProvider Queries**
   - Modify `getBy()` method to include user relation
   - Update `findByIdWithRelations()` to include user by default
   - Ensure all video queries can optionally include user data

3. **Update VideoResponseMapper**
   - Modify `map()` method to populate user field from entity.user
   - Use UserResponseMapper to properly format user data
   - Handle cases where user relation might be null

4. **Update Controller Usage**
   - Ensure controller calls provider methods with user relation included
   - Verify both public and authenticated endpoints include user data

## Testing Requirements

### Unit Tests
- Test VideoResponseMapper with user relation populated
- Test VideoProvider queries include user relation
- Test user data formatting in response

### Integration Tests
- Test GET /video/{id} returns complete user object
- Test user object includes expected fields (username, profile picture, etc.)
- Test both public and authenticated access scenarios

## Dependencies
None - this is an independent fix

## Acceptance Criteria

1. **User Object Populated**
   - GET /video/{id} responses include complete user object
   - User object contains all public user fields (username, profilePicture, etc.)
   - User data is properly formatted using existing UserResponseMapper

2. **Performance Maintained**
   - User relation loading doesn't significantly impact response times
   - Database queries are optimized (proper JOINs, not N+1 queries)
   - Caching considerations addressed if applicable

3. **Backward Compatibility**
   - Existing API consumers continue to work
   - No breaking changes to response structure
   - User field is always present (not conditionally included)

4. **Error Handling**
   - Handle cases where user relation might be missing
   - Graceful degradation if user data unavailable
   - Proper error responses for invalid video IDs

## Implementation Details

### VideoEntity Relation Check
```typescript
// Verify this relation exists and is properly configured
@ManyToOne(() => UserEntity, { eager: false })
@JoinColumn({ name: 'userId' })
user: UserEntity;
```

### VideoProvider Update
```typescript
// Update getBy method to include user relation
async getBy(criteria: any): Promise<VideoEntity> {
  return this.repository.findOne({
    where: criteria,
    relations: ['user', 'originalImageCompletion'] // Add user relation
  });
}
```

### VideoResponseMapper Update
```typescript
// Update map method to populate user field
async map(entity: VideoEntity, userId: string = null): Promise<VideoDto> {
  const dto = new VideoDto();
  // ... existing mapping ...
  
  // Add user mapping
  if (entity.user) {
    dto.user = this.userResponseMapper.mapPublic(entity.user);
  }
  
  return dto;
}
```

### Expected Response Structure
```json
{
  "id": "video-uuid",
  "user": {
    "id": "user-uuid",
    "username": "creator_username",
    "name": "Creator Name",
    "profilePicture": "https://cdn.example.com/profile.jpg",
    "isVerified": false
  },
  "originalImageCompletion": { ... },
  "videoVersions": { ... }
}
```
