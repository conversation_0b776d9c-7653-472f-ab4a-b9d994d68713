# Task 01: Fix User Feed Privacy and ModelIds Filters

## Priority
High

## Category
User Feed Privacy/ModelIds Filters (Bug #1)

## Description
Investigate and fix privacy and modelIds filtering in GET /feed/user/{userIdentifier} endpoint. The API documentation and DTO already define these parameters, but there may be implementation gaps in the service layer where these filters are not being properly applied to database queries.

## Technical Details

### Files to Modify
- `src/feed/service/feed.service.ts` - Main feed service implementation
- `src/feed/dto/user-feed-request.dto.ts` - Request DTO (already has filters defined)
- `src/feed/controller/feed.controller.ts` - Controller endpoint

### Key Components
- **Controller Method**: `FeedController.getUserFeed()`
- **Service Method**: `FeedService.getUserFeed()`
- **DTO**: `UserFeedRequestDto` (privacy, modelIds fields)
- **Endpoint**: `GET /feed/user/{userIdentifier}`

### Current Implementation Status
- Privacy filter enum: `UserFeedPrivacyFilter` (PUBLIC, PRIVATE, ALL)
- ModelIds array: `string[]` with UUID validation
- API documentation: Complete with examples
- Service implementation: Needs investigation

## Implementation Steps

1. **Audit Current Implementation**
   - Review `FeedService.getUserFeed()` method
   - Check if privacy and modelIds filters are passed to database queries
   - Identify where filtering logic should be applied

2. **Implement Privacy Filtering**
   - Add privacy-based WHERE clauses to feed queries
   - Ensure non-owners can only see PUBLIC content
   - Allow owners to see PRIVATE and ALL content based on filter

3. **Implement ModelIds Filtering**
   - Add JOIN or WHERE clause to filter by model UUIDs
   - Handle empty array (no filtering) vs populated array (specific models)
   - Ensure proper UUID validation

4. **Update Query Builder**
   - Modify feed entity queries to include privacy and model filters
   - Ensure proper SQL generation for both filters combined

## Testing Requirements

### Unit Tests
- Test privacy filter with different user permissions
- Test modelIds array filtering with various UUID combinations
- Test combined privacy + modelIds filtering

### Integration Tests
- Test endpoint with authenticated owner vs non-owner
- Test with invalid modelIds (should return 400)
- Test edge cases (empty arrays, null values)

## Dependencies
None - this is a foundational fix

## Acceptance Criteria

1. **Privacy Filter Works**
   - `privacy=public` shows only public content to all users
   - `privacy=private` shows private content only to content owners
   - `privacy=all` shows all content only to content owners
   - Non-owners cannot access private content regardless of filter

2. **ModelIds Filter Works**
   - Empty/null modelIds array returns content from all models
   - Populated modelIds array returns only content from specified models
   - Invalid UUIDs return proper validation errors

3. **Combined Filters Work**
   - Both filters can be applied simultaneously
   - Results respect both privacy and model constraints

4. **API Documentation Matches Implementation**
   - Swagger docs accurately reflect filter behavior
   - Examples work as documented

## Related Code References
```typescript
// UserFeedRequestDto already defines:
privacy?: UserFeedPrivacyFilter = UserFeedPrivacyFilter.PUBLIC;
modelIds?: string[]; // UUID validation included

// Controller already accepts:
@Query() request: UserFeedRequestDto
```
