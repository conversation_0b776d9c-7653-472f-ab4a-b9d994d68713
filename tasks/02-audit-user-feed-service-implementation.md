# Task 02: Audit UserFeedService.getUserFeed Implementation

## Priority
High

## Category
User Feed Privacy/ModelIds Filters (Bug #1)

## Description
Review the getUserFeed method in FeedService to ensure privacy and modelIds filters from UserFeedRequestDto are properly applied to the database queries. This is a diagnostic task to identify exactly where the filtering logic is missing or broken.

## Technical Details

### Files to Analyze
- `src/feed/service/feed.service.ts` - Main implementation
- `src/feed/service/feed.service.ts:getUserFeed()` method
- Database query builders used in user feed generation

### Key Investigation Points
- **Query Building**: How user feed queries are constructed
- **Filter Application**: Where UserFeedRequestDto filters should be applied
- **Database Relations**: What tables/entities are joined for user feeds
- **Privacy Logic**: Current privacy filtering implementation
- **Model Filtering**: Current model-based filtering implementation

### Current Code Structure
```typescript
// From feed.controller.ts
async getUserFeed(
  @Request() req,
  @Param('userIdentifier') userIdentifier: string,
  @Query() request: UserFeedRequestDto,
): Promise<FeedResponseDto>

// Expected in feed.service.ts
async getUserFeed(
  userIdentifier: string,
  currentUserId: string | null,
  request: UserFeedRequestDto,
): Promise<FeedResponseDto>
```

## Implementation Steps

1. **Trace Request Flow**
   - Follow UserFeedRequestDto from controller to service
   - Identify where privacy and modelIds parameters are used
   - Document current query building process

2. **Analyze Database Queries**
   - Review SQL generation for user feeds
   - Check if privacy fields are included in WHERE clauses
   - Verify model filtering in JOINs or WHERE conditions

3. **Identify Missing Logic**
   - Compare with working filter implementations in other endpoints
   - Document gaps in privacy filtering
   - Document gaps in modelIds filtering

4. **Document Current State**
   - Create detailed analysis of what works vs what doesn't
   - Identify specific code locations needing fixes
   - Prioritize fixes based on impact

## Testing Requirements

### Diagnostic Tests
- Create test cases that expose current filtering failures
- Test with various privacy settings and modelIds combinations
- Document expected vs actual behavior

### Code Analysis
- Review similar filtering implementations in discovery/board feeds
- Compare with image completion filtering logic
- Identify reusable patterns

## Dependencies
None - this is a prerequisite analysis task

## Acceptance Criteria

1. **Complete Analysis Document**
   - Detailed flow of UserFeedRequestDto through the system
   - Specific locations where filtering should occur
   - Current implementation gaps identified

2. **Test Cases Created**
   - Failing tests that demonstrate current issues
   - Clear expected vs actual behavior documentation

3. **Fix Strategy Defined**
   - Specific code changes needed for privacy filtering
   - Specific code changes needed for modelIds filtering
   - Implementation approach documented

4. **Code Locations Identified**
   - Exact methods and lines needing modification
   - Database query builders requiring updates
   - Filter application points mapped

## Investigation Checklist

- [ ] Trace UserFeedRequestDto.privacy usage
- [ ] Trace UserFeedRequestDto.modelIds usage  
- [ ] Review user feed query building logic
- [ ] Compare with working filter implementations
- [ ] Document missing filter applications
- [ ] Create failing test cases
- [ ] Define fix implementation strategy

## Related Code References
```typescript
// Expected filter usage patterns:
if (request.privacy === UserFeedPrivacyFilter.PUBLIC) {
  queryBuilder.andWhere('entity.privacy = :privacy', { privacy: 'public' });
}

if (request.modelIds?.length > 0) {
  queryBuilder.andWhere('entity.modelId IN (:...modelIds)', { modelIds: request.modelIds });
}
```
